import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import '../../lib/services/data_stream_service.dart';
import '../../lib/services/offline_contact_service.dart';
import '../../lib/models/models.dart';

// Generate mocks
@GenerateMocks([OfflineContactService])
import 'data_stream_service_test.mocks.dart';

void main() {
  group('DataStreamService Tests', () {
    late DataStreamService dataStreamService;
    late MockOfflineContactService mockOfflineService;

    setUp(() {
      mockOfflineService = MockOfflineContactService();
      dataStreamService = DataStreamService();
      
      // Mock the offline service initialization
      when(mockOfflineService.initialize()).thenAnswer((_) async {});
      when(mockOfflineService.isOnline).thenReturn(true);
      when(mockOfflineService.isSyncing).thenReturn(false);
    });

    tearDown(() {
      dataStreamService.dispose();
    });

    test('should initialize successfully', () async {
      // Mock required streams
      when(mockOfflineService.profilesStream).thenAnswer(
        (_) => Stream.fromIterable([<Profile>[]]),
      );
      when(mockOfflineService.categoriesStream).thenAnswer(
        (_) => Stream.fromIterable([<Category>[]]),
      );
      when(mockOfflineService.contactAssignmentsStream).thenAnswer(
        (_) => Stream.fromIterable([<String, Category?>{}]),
      );
      when(mockOfflineService.syncStatusStream).thenAnswer(
        (_) => Stream.fromIterable([false]),
      );

      await dataStreamService.initialize();
      
      expect(dataStreamService.currentLoadingState, DataLoadingState.initial);
      verify(mockOfflineService.initialize()).called(1);
    });

    test('should emit loading states correctly', () async {
      // Mock required streams
      when(mockOfflineService.profilesStream).thenAnswer(
        (_) => Stream.fromIterable([<Profile>[]]),
      );
      when(mockOfflineService.categoriesStream).thenAnswer(
        (_) => Stream.fromIterable([<Category>[]]),
      );
      when(mockOfflineService.contactAssignmentsStream).thenAnswer(
        (_) => Stream.fromIterable([<String, Category?>{}]),
      );
      when(mockOfflineService.syncStatusStream).thenAnswer(
        (_) => Stream.fromIterable([false]),
      );
      when(mockOfflineService.cachedProfiles).thenReturn([]);
      when(mockOfflineService.refreshCache()).thenAnswer((_) async {});

      final loadingStates = <DataLoadingState>[];
      dataStreamService.loadingStateStream.listen((state) {
        loadingStates.add(state);
      });

      await dataStreamService.initialize();
      await dataStreamService.loadContactsData();

      // Should go through loading -> loaded states
      expect(loadingStates, contains(DataLoadingState.loading));
      expect(loadingStates, contains(DataLoadingState.loaded));
    });

    test('should handle contact assignment updates', () async {
      // Mock required streams
      when(mockOfflineService.profilesStream).thenAnswer(
        (_) => Stream.fromIterable([<Profile>[]]),
      );
      when(mockOfflineService.categoriesStream).thenAnswer(
        (_) => Stream.fromIterable([<Category>[]]),
      );
      when(mockOfflineService.contactAssignmentsStream).thenAnswer(
        (_) => Stream.fromIterable([<String, Category?>{}]),
      );
      when(mockOfflineService.syncStatusStream).thenAnswer(
        (_) => Stream.fromIterable([false]),
      );
      when(mockOfflineService.assignContactToCategory(
        userId: anyNamed('userId'),
        contactPhone: anyNamed('contactPhone'),
        categoryId: anyNamed('categoryId'),
      )).thenAnswer((_) async {});

      await dataStreamService.initialize();

      // Test contact assignment
      await dataStreamService.assignContactToCategory(
        userId: 'user123',
        contactPhone: '1234567890',
        categoryId: 'category123',
      );

      verify(mockOfflineService.assignContactToCategory(
        userId: 'user123',
        contactPhone: '1234567890',
        categoryId: 'category123',
      )).called(1);
    });

    test('should handle errors gracefully', () async {
      // Mock required streams
      when(mockOfflineService.profilesStream).thenAnswer(
        (_) => Stream.fromIterable([<Profile>[]]),
      );
      when(mockOfflineService.categoriesStream).thenAnswer(
        (_) => Stream.fromIterable([<Category>[]]),
      );
      when(mockOfflineService.contactAssignmentsStream).thenAnswer(
        (_) => Stream.fromIterable([<String, Category?>{}]),
      );
      when(mockOfflineService.syncStatusStream).thenAnswer(
        (_) => Stream.fromIterable([false]),
      );
      when(mockOfflineService.refreshCache()).thenThrow(Exception('Network error'));

      final errors = <String?>[];
      dataStreamService.errorStream.listen((error) {
        errors.add(error);
      });

      await dataStreamService.initialize();
      await dataStreamService.loadContactsData();

      // Should emit error
      expect(errors, isNotEmpty);
      expect(errors.last, contains('Network error'));
    });

    test('should perform force sync correctly', () async {
      // Mock required streams
      when(mockOfflineService.profilesStream).thenAnswer(
        (_) => Stream.fromIterable([<Profile>[]]),
      );
      when(mockOfflineService.categoriesStream).thenAnswer(
        (_) => Stream.fromIterable([<Category>[]]),
      );
      when(mockOfflineService.contactAssignmentsStream).thenAnswer(
        (_) => Stream.fromIterable([<String, Category?>{}]),
      );
      when(mockOfflineService.syncStatusStream).thenAnswer(
        (_) => Stream.fromIterable([false]),
      );
      when(mockOfflineService.invalidateAllCaches()).thenReturn(null);
      when(mockOfflineService.smartSync()).thenAnswer((_) async {});

      await dataStreamService.initialize();
      await dataStreamService.forceSync();

      verify(mockOfflineService.invalidateAllCaches()).called(1);
      verify(mockOfflineService.smartSync()).called(1);
    });

    test('should handle real-time updates from streams', () async {
      final profilesController = StreamController<List<Profile>>();
      final categoriesController = StreamController<List<Category>>();
      final assignmentsController = StreamController<Map<String, Category?>>();
      final syncStatusController = StreamController<bool>();

      when(mockOfflineService.profilesStream).thenAnswer(
        (_) => profilesController.stream,
      );
      when(mockOfflineService.categoriesStream).thenAnswer(
        (_) => categoriesController.stream,
      );
      when(mockOfflineService.contactAssignmentsStream).thenAnswer(
        (_) => assignmentsController.stream,
      );
      when(mockOfflineService.syncStatusStream).thenAnswer(
        (_) => syncStatusController.stream,
      );
      when(mockOfflineService.cachedProfiles).thenReturn([]);

      final contactsUpdates = <List<ContactWithProfile>>[];
      dataStreamService.contactsStream.listen((contacts) {
        contactsUpdates.add(contacts);
      });

      await dataStreamService.initialize();

      // Simulate profile updates
      final testProfile = Profile(
        id: 'profile1',
        phoneNumber: '+1234567890',
        fullName: 'Test User',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      profilesController.add([testProfile]);
      await Future.delayed(Duration(milliseconds: 100)); // Allow stream processing

      // Should trigger contacts list rebuild
      expect(contactsUpdates, isNotEmpty);

      // Clean up
      await profilesController.close();
      await categoriesController.close();
      await assignmentsController.close();
      await syncStatusController.close();
    });
  });
}
