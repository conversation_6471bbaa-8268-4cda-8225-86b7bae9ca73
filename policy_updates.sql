-- SQL Policy Updates for Contact Details Feature with International Phone Number Support
-- Run these commands in your Supabase SQL editor

-- First, drop the existing problematic policies
DROP POLICY IF EXISTS "Users can view assignments where they are the contact" ON public.user_contacts;
DROP POLICY IF EXISTS "Users can view categories assigned to them" ON public.categories;
DROP POLICY IF EXISTS "Users can view time slots for categories assigned to them" ON public.time_slots;

-- <PERSON>reate corrected policies that handle international phone number normalization

-- Allow users to view contact assignments where they are the categorized contact
-- This enables User B to see how User A categorized them
-- Updated to handle international phone number normalization
CREATE POLICY "Users can view assignments where they are the contact" ON public.user_contacts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.id = auth.uid()
            AND normalize_phone_number(profiles.phone_number) = normalize_phone_number(user_contacts.categorized_contact_phone)
        )
    );

-- Allow users to view categories that were assigned to them by others
-- This enables User B to see categories that User A assigned to them
-- Updated to handle international phone number normalization
CREATE POLICY "Users can view categories assigned to them" ON public.categories
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_contacts
            JOIN public.profiles ON profiles.id = auth.uid()
            WHERE user_contacts.assigned_category_id = categories.id
            AND normalize_phone_number(profiles.phone_number) = normalize_phone_number(user_contacts.categorized_contact_phone)
        )
    );

-- Allow users to view time slots for categories that were assigned to them
-- This enables User B to see time slots for categories that User A assigned to them
-- Updated to handle international phone number normalization
CREATE POLICY "Users can view time slots for categories assigned to them" ON public.time_slots
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.categories
            JOIN public.user_contacts ON user_contacts.assigned_category_id = categories.id
            JOIN public.profiles ON profiles.id = auth.uid()
            WHERE categories.id = time_slots.category_id
            AND normalize_phone_number(profiles.phone_number) = normalize_phone_number(user_contacts.categorized_contact_phone)
        )
    );
-- First, drop the existing problematic policies
DROP POLICY IF EXISTS "Users can view assignments where they are the contact" ON public.user_contacts;
DROP POLICY IF EXISTS "Users can view categories assigned to them" ON public.categories;
DROP POLICY IF EXISTS "Users can view time slots for categories assigned to them" ON public.time_slots;

-- Create corrected policies that handle phone number normalization

-- Allow users to view contact assignments where they are the categorized contact
-- This enables User B to see how User A categorized them
-- Updated to handle phone number normalization (removing country codes and + prefix)
CREATE POLICY "Users can view assignments where they are the contact" ON public.user_contacts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND (
                -- Direct match
                profiles.phone_number = user_contacts.categorized_contact_phone
                OR
                -- Match without + prefix
                REPLACE(profiles.phone_number, '+', '') = user_contacts.categorized_contact_phone
                OR
                -- Match normalized versions (remove country code 967 and + prefix)
                CASE 
                    WHEN profiles.phone_number LIKE '+967%' THEN SUBSTRING(profiles.phone_number FROM 5)
                    WHEN profiles.phone_number LIKE '967%' THEN SUBSTRING(profiles.phone_number FROM 4)
                    ELSE REPLACE(profiles.phone_number, '+', '')
                END = 
                CASE 
                    WHEN user_contacts.categorized_contact_phone LIKE '+967%' THEN SUBSTRING(user_contacts.categorized_contact_phone FROM 5)
                    WHEN user_contacts.categorized_contact_phone LIKE '967%' THEN SUBSTRING(user_contacts.categorized_contact_phone FROM 4)
                    ELSE REPLACE(user_contacts.categorized_contact_phone, '+', '')
                END
            )
        )
    );

-- Allow users to view categories that were assigned to them by others
-- This enables User B to see categories that User A assigned to them
-- Updated to handle phone number normalization
CREATE POLICY "Users can view categories assigned to them" ON public.categories
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_contacts 
            JOIN public.profiles ON profiles.id = auth.uid()
            WHERE user_contacts.assigned_category_id = categories.id 
            AND (
                -- Direct match
                profiles.phone_number = user_contacts.categorized_contact_phone
                OR
                -- Match without + prefix
                REPLACE(profiles.phone_number, '+', '') = user_contacts.categorized_contact_phone
                OR
                -- Match normalized versions (remove country code 967 and + prefix)
                CASE 
                    WHEN profiles.phone_number LIKE '+967%' THEN SUBSTRING(profiles.phone_number FROM 5)
                    WHEN profiles.phone_number LIKE '967%' THEN SUBSTRING(profiles.phone_number FROM 4)
                    ELSE REPLACE(profiles.phone_number, '+', '')
                END = 
                CASE 
                    WHEN user_contacts.categorized_contact_phone LIKE '+967%' THEN SUBSTRING(user_contacts.categorized_contact_phone FROM 5)
                    WHEN user_contacts.categorized_contact_phone LIKE '967%' THEN SUBSTRING(user_contacts.categorized_contact_phone FROM 4)
                    ELSE REPLACE(user_contacts.categorized_contact_phone, '+', '')
                END
            )
        )
    );

-- Allow users to view time slots for categories that were assigned to them
-- This enables User B to see time slots for categories that User A assigned to them
-- Updated to handle phone number normalization
CREATE POLICY "Users can view time slots for categories assigned to them" ON public.time_slots
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.categories 
            JOIN public.user_contacts ON user_contacts.assigned_category_id = categories.id
            JOIN public.profiles ON profiles.id = auth.uid()
            WHERE categories.id = time_slots.category_id 
            AND (
                -- Direct match
                profiles.phone_number = user_contacts.categorized_contact_phone
                OR
                -- Match without + prefix
                REPLACE(profiles.phone_number, '+', '') = user_contacts.categorized_contact_phone
                OR
                -- Match normalized versions (remove country code 967 and + prefix)
                CASE 
                    WHEN profiles.phone_number LIKE '+967%' THEN SUBSTRING(profiles.phone_number FROM 5)
                    WHEN profiles.phone_number LIKE '967%' THEN SUBSTRING(profiles.phone_number FROM 4)
                    ELSE REPLACE(profiles.phone_number, '+', '')
                END = 
                CASE 
                    WHEN user_contacts.categorized_contact_phone LIKE '+967%' THEN SUBSTRING(user_contacts.categorized_contact_phone FROM 5)
                    WHEN user_contacts.categorized_contact_phone LIKE '967%' THEN SUBSTRING(user_contacts.categorized_contact_phone FROM 4)
                    ELSE REPLACE(user_contacts.categorized_contact_phone, '+', '')
                END
            )
        )
    );