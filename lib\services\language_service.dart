import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/profile.dart';
import 'supabase_service.dart';

class LanguageService extends ChangeNotifier {
  static const String _languageKey = 'preferred_language';
  
  Locale _currentLocale = const Locale('ar'); // Default to Arabic
  
  Locale get currentLocale => _currentLocale;
  
  static final LanguageService _instance = LanguageService._internal();
  factory LanguageService() => _instance;
  LanguageService._internal();
  
  /// Initialize language service and load saved language preference
  Future<void> initialize() async {
    try {
      // First check shared preferences for saved language
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString(_languageKey);

      if (savedLanguage != null) {
        // User has previously selected a language, use it
        _currentLocale = Locale(savedLanguage);
        print('📱 Language Service: Using saved language: $savedLanguage');
      } else {
        // No saved preference, try to get from user profile if authenticated
        try {
          final profile = await SupabaseService.getCurrentProfile();
          if (profile?.preferredLanguage != null) {
            _currentLocale = Locale(profile!.preferredLanguage!);
            await _saveLanguageToPrefs(profile.preferredLanguage!);
            print('📱 Language Service: Using profile language: ${profile.preferredLanguage}');
          } else {
            // No profile language, use Arabic as default
            _currentLocale = const Locale('ar');
            await _saveLanguageToPrefs('ar');
            print('📱 Language Service: Using default language: ar');
          }
        } catch (e) {
          // Error getting profile, use Arabic as default
          _currentLocale = const Locale('ar');
          await _saveLanguageToPrefs('ar');
          print('📱 Language Service: Profile error, using default language: ar');
        }
      }

      notifyListeners();
      print('📱 Language Service: Initialized with locale: ${_currentLocale.languageCode}');
    } catch (e) {
      print('❌ Error initializing language service: $e');
      // Default to Arabic if there's an error
      _currentLocale = const Locale('ar');
      print('📱 Language Service: Error fallback to Arabic');
    }
  }
  
  /// Change the app language
  Future<void> changeLanguage(String languageCode) async {
    try {
      print('📱 Language Service: Changing language to: $languageCode');

      _currentLocale = Locale(languageCode);
      notifyListeners();

      // Save to shared preferences first (most important for persistence)
      await _saveLanguageToPrefs(languageCode);
      print('📱 Language Service: Saved language to preferences: $languageCode');

      // Update user profile if authenticated (secondary)
      await _updateProfileLanguage(languageCode);

      print('📱 Language Service: Language change completed: $languageCode');
    } catch (e) {
      print('❌ Error changing language: $e');
    }
  }
  
  /// Save language preference to shared preferences
  Future<void> _saveLanguageToPrefs(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
    } catch (e) {
      print('❌ Error saving language to preferences: $e');
    }
  }
  
  /// Update user profile with new language preference
  Future<void> _updateProfileLanguage(String languageCode) async {
    try {
      final profile = await SupabaseService.getCurrentProfile();
      if (profile != null) {
        final updatedProfile = profile.copyWith(
          preferredLanguage: languageCode,
          updatedAt: DateTime.now(),
        );
        await SupabaseService.updateProfile(updatedProfile);
      }
    } catch (e) {
      print('❌ Error updating profile language: $e');
      // Don't throw error - language change should still work locally
    }
  }
  
  /// Get list of supported languages
  List<Map<String, String>> getSupportedLanguages() {
    return [
      {'code': 'en', 'name': 'English', 'nativeName': 'English'},
      {'code': 'ar', 'name': 'Arabic', 'nativeName': 'العربية'},
    ];
  }
  
  /// Check if current language is RTL
  bool get isRTL => _currentLocale.languageCode == 'ar';
  
  /// Get text direction based on current language
  TextDirection get textDirection => isRTL ? TextDirection.rtl : TextDirection.ltr;

  /// Reset language to default (Arabic)
  Future<void> resetToDefault() async {
    await changeLanguage('ar');
  }

  /// Check if current language is the default
  bool get isDefaultLanguage => _currentLocale.languageCode == 'ar';
}
