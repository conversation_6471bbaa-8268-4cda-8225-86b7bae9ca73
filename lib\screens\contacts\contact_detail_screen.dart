// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../models/models.dart';
import '../../models/category_extensions.dart';
import '../../services/supabase_service.dart';
import '../../services/contacts_service.dart';
import '../../services/calling_service.dart';
import '../../services/notification_service.dart';
import '../../services/offline_contact_service.dart';
import '../../services/connectivity_service.dart';
import '../../services/data_stream_service.dart';
import '../../widgets/ltr_override.dart';

class ContactDetailScreen extends StatefulWidget {
  final ContactWithProfile contact;

  const ContactDetailScreen({
    super.key,
    required this.contact,
  });

  @override
  State<ContactDetailScreen> createState() => _ContactDetailScreenState();
}

class _ContactDetailScreenState extends State<ContactDetailScreen> {
  Category? _contactCategory;
  bool _isLoading = true;
  String? _error;
  List<NotificationPreference> _notificationPreferences = [];
  Set<String> _enabledNotifications = {};

  // Offline services
  final OfflineContactService _offlineService = OfflineContactService();
  final ConnectivityService _connectivityService = ConnectivityService();

  @override
  void initState() {
    super.initState();
    _initializeOfflineServices();
    _loadContactCategory();
  }

  Future<void> _initializeOfflineServices() async {
    try {
      print('📱 Initializing offline services for contact detail screen...');
      await _offlineService.initialize();
      print('✅ Offline services initialized successfully');
    } catch (e) {
      print('❌ Failed to initialize offline services: $e');
    }
  }

  Future<void> _loadContactCategory() async {
    try {
      // Safety check - this screen should only be used for contacts with app accounts
      if (widget.contact.profile == null) {
        if (mounted) {
          setState(() {
            _error = 'This contact does not have an app account';
            _isLoading = false;
          });
        }
        return;
      }

      if (mounted) {
        setState(() {
          _isLoading = true;
          _error = null;
        });
      }

      print('🔍 Loading contact category for: ${widget.contact.profile!.fullName}');
      print('🔍 Contact profile ID: ${widget.contact.profile!.id}');
      print('🔍 Contact phone: ${widget.contact.profile!.phoneNumber}');

      final currentProfile = await _offlineService.getCurrentProfile();
      if (currentProfile == null) {
        print('❌ Current profile is null');
        if (mounted) {
          setState(() {
            _error = AppLocalizations.of(context)?.currentUserProfileNotFound ?? 'Current user profile not found';
            _isLoading = false;
          });
        }
        return;
      }

      print('🔍 Current user ID: ${currentProfile.id}');
      print('🔍 Current user phone: ${currentProfile.phoneNumber}');

      // Get the category that this contact (User B) assigned to the current user (User A)
      // So we look for user_contacts where:
      // - user_id = contact's ID (User B who did the categorizing)
      // - categorized_contact_phone = current user's phone (User A who was categorized)
      // This shows User A how User B wants to be contacted (User B's preferences)
      // When User B categorized User A, User B was setting their own communication preferences
      final category = await _offlineService.getContactCategoryWithPhones(
        contactUserId: widget.contact.profile!.id,     // User B (contact who did the categorizing)
        callerPhone: currentProfile.phoneNumber,      // User A's phone (current user who was categorized)
        contactPhoneNumbers: widget.contact.deviceContact.phoneNumbers, // All contact's phone numbers
      );

      print('🔍 Retrieved category: ${category?.type.name ?? 'null'}');
      if (category != null) {
        print('🔍 Category note: ${category.note}');
        print('🔍 Category time slots: ${category.timeSlots.length}');
      }

      // Load notification preferences (offline-aware)
      if (currentProfile != null) {
        try {
          final preferences = await _offlineService.getNotificationPreferences(
            userId: currentProfile.id,
            contactUserId: widget.contact.profile!.id,
          );

          _notificationPreferences = preferences;
          _enabledNotifications = preferences
              .where((pref) => pref.isEnabled)
              .map((pref) => pref.timeSlotId)
              .toSet().cast<String>();
        } catch (e) {
          print('❌ Failed to load notification preferences: $e');
          // Continue without notification preferences
          _notificationPreferences = [];
          _enabledNotifications = {};
        }
      } else {
        print('📱 No current profile - skipping notification preferences');
        _notificationPreferences = [];
        _enabledNotifications = {};
      }

      if (mounted) {
        setState(() {
          _contactCategory = category;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('❌ Error loading contact category: $e');
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _toggleNotification(String timeSlotId) async {
    try {
      final currentProfile = await _offlineService.getCurrentProfile();
      if (currentProfile == null) return;

      final isCurrentlyEnabled = _enabledNotifications.contains(timeSlotId);
      final newState = !isCurrentlyEnabled;

      print('📱 Toggling notification offline-aware...');
      print('📱   Time slot: $timeSlotId');
      print('📱   Current state: $isCurrentlyEnabled');
      print('📱   New state: $newState');
      print('📱   Online: ${_connectivityService.isOnline}');

      // Request basic notification permission if this is the first time enabling
      if (newState) {
        await NotificationService.requestPermissions();
      }

      // Use offline-aware service
      try {
        await _offlineService.updateNotificationPreference(
          userId: currentProfile.id,
          contactUserId: widget.contact.profile!.id,
          timeSlotId: timeSlotId,
          isEnabled: newState,
        );

        // Schedule notification if enabled
        if (newState) {
          await _offlineService.scheduleNotification(
            contactPhone: widget.contact.profile!.phoneNumber,
            contactName: widget.contact.profile!.fullName ?? widget.contact.profile!.phoneNumber,
            timeSlotId: timeSlotId,
            scheduledTime: DateTime.now(), // This would be calculated based on the time slot
          );
        }

        final statusText = _connectivityService.isOnline ? '' : ' (offline)';
        print('✅ Notification preference saved$statusText');
      } catch (e) {
        print('❌ Failed to save notification preference: $e');
      }

      if (mounted) {
        setState(() {
          if (newState) {
            _enabledNotifications.add(timeSlotId);
          } else {
            _enabledNotifications.remove(timeSlotId);
          }
        });
      }

      if (mounted) {
        final statusText = _connectivityService.isOnline ? '' : ' (offline)';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              newState
                ? 'Notification enabled for this time slot$statusText'
                : 'Notification disabled for this time slot$statusText',
            ),
            backgroundColor: newState ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 2),
          ),
        );

        // Reschedule notifications (works offline too)
        NotificationService.scheduleContactNotifications();
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = AppLocalizations.of(context)?.errorUpdatingNotification ?? 'Error updating notification';
        if (e.toString().contains('duplicate key')) {
          errorMessage = AppLocalizations.of(context)?.notificationPreferenceExists ?? 'Notification preference already exists. Please try again.';
        } else if (e.toString().contains('network') || e.toString().contains('connection')) {
          errorMessage = AppLocalizations.of(context)?.networkErrorCheckConnection ?? 'Network error. Please check your connection and try again.';
        } else {
          errorMessage = AppLocalizations.of(context)?.errorUpdatingNotificationWithDetails(e.toString()) ?? 'Error updating notification: ${e.toString()}';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Future<void> _makeCall() async {
    try {
      final currentProfile = await _offlineService.getCurrentProfile();
      if (currentProfile == null) return;

      // Handle multiple phone numbers
      if (widget.contact.deviceContact.phoneNumbers.length == 1) {
        // Only one phone number, call directly
        await CallingService.makeCall(
          contactProfile: widget.contact.profile!,
          contactCategory: _contactCategory,
          callerName: currentProfile.fullName ?? currentProfile.phoneNumber,
          specificPhoneNumber: widget.contact.deviceContact.phoneNumbers.first,
        );
      } else {
        // Multiple phone numbers, show selection dialog
        await _showPhoneSelectionDialog();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)?.failedToMakeCall(e.toString()) ?? 'Failed to make call: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _showPhoneSelectionDialog() async {
    final currentProfile = await _offlineService.getCurrentProfile();
    if (currentProfile == null) return;

    if (!mounted) return;

    return showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.green.shade50,
                Colors.blue.shade50,
              ],
            ),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.green.shade400, Colors.green.shade600],
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.withOpacity(0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.phone,
                  color: Colors.white,
                  size: 30,
                ),
              ),
              const SizedBox(height: 20),

              Text(
                AppLocalizations.of(context)?.selectPhoneNumber ?? 'Select Phone Number',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),

              // Phone numbers list
              for (final phoneNumber in widget.contact.deviceContact.phoneNumbers)
                Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(16),
                      onTap: () async {
                        Navigator.of(context).pop();
                        // Call with the selected phone number
                        await CallingService.makeCall(
                          contactProfile: widget.contact.profile!,
                          contactCategory: _contactCategory,
                          callerName: currentProfile.fullName ?? currentProfile.phoneNumber,
                          specificPhoneNumber: phoneNumber,
                        );
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.green.shade100,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.phone,
                                color: Colors.green.shade600,
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Text(
                                ContactsService.formatPhoneNumber(phoneNumber),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey[800],
                                ),
                              ),
                            ),
                            Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.grey[400],
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

              const SizedBox(height: 20),

              // Cancel button
              Container(
                width: double.infinity,
                height: 50,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: () => Navigator.of(context).pop(),
                    child: Center(
                      child: Text(
                        AppLocalizations.of(context)?.cancel ?? 'Cancel',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black87),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.9),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: const Icon(Icons.phone, color: Colors.white),
              onPressed: _makeCall,
            ),
          ),
        ],
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
              Color(0xFF667eea),
            ],
            stops: [0.0, 0.5, 1.0],
          ),
        ),
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return Center(
        child: Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
              ),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context)?.loadingContactDetails ?? 'Loading contact details...',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Container(
          margin: const EdgeInsets.all(20),
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.red.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  Icons.error_outline,
                  size: 48,
                  color: Colors.red[400],
                ),
              ),
              const SizedBox(height: 20),
              Text(
                AppLocalizations.of(context)?.oopsSomethingWentWrong ?? 'Oops! Something went wrong',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _loadContactCategory,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 8,
                  shadowColor: Colors.blue.withOpacity(0.3),
                ),
                child: Text(
                  AppLocalizations.of(context)?.tryAgain ?? 'Try Again',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(20, 120, 20, 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
          // Modern Contact Info Card
          Container(
            margin: const EdgeInsets.only(bottom: 24),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.25),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.15),
                  blurRadius: 15,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(28),
              child: Row(
                children: [
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.white.withOpacity(0.3),
                          Colors.white.withOpacity(0.1),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.4),
                        width: 2,
                      ),
                    ),
                    child: widget.contact.deviceContact.avatar != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(20),
                            child: _buildSafeAvatar(),
                          )
                        : Center(
                            child: Text(
                              widget.contact.displayName.isNotEmpty
                                  ? widget.contact.displayName[0].toUpperCase()
                                  : '?',
                              style: const TextStyle(
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.contact.displayName,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 4),
                        LTROverride(
                          child: Text(
                            ContactsService.formatPhoneNumber(widget.contact.profile!.phoneNumber),
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white.withOpacity(0.7),
                            ),
                          ),
                        ),
                        if (widget.contact.deviceContact.email != null) ...[
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(Icons.email, size: 16, color: Colors.grey[600]),
                              const SizedBox(width: 6),
                              Text(
                                widget.contact.deviceContact.email!,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.green[400]!, Colors.green[600]!],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.green.withOpacity(0.3),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(16),
                        onTap: _makeCall,
                        child: const Padding(
                          padding: EdgeInsets.all(16),
                          child: Icon(
                            Icons.phone,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Modern Communication Preferences Card
          Container(
            margin: const EdgeInsets.only(bottom: 24),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.25),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.15),
                  blurRadius: 15,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(28),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          _contactCategory != null
                              ? _getCategoryIcon(_contactCategory!.type)
                              : Icons.help_outline,
                          size: 24,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 20),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context)?.contactPreferences(widget.contact.profile!.fullName?.split(' ').first ?? AppLocalizations.of(context)?.contact ?? 'Contact') ?? '${widget.contact.profile!.fullName?.split(' ').first ?? 'Contact'}\'s Preferences',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            Text(
                              AppLocalizations.of(context)?.communicationPreferences ?? 'Communication preferences',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),

                  if (_contactCategory != null) ...[
                    _buildCategoryInfo(),
                    if (_contactCategory!.timeSlots.isNotEmpty) ...[
                      const SizedBox(height: 24),
                      _buildTimeSlots(),
                    ],
                  ] else ...[
                    _buildNoPreferences(),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Calling Suggestion Card
          Card(
            color: _getCallingSuggestionColor(),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getCallingSuggestionIcon(),
                        color: Colors.white,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context)?.callingSuggestion ?? 'Calling Suggestion',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    CallingService.getCallingSuggestion(_contactCategory, context),
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Call Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _makeCall,
              icon: const Icon(Icons.phone, color: Colors.white),
              label: Text(
                AppLocalizations.of(context)?.callNow ?? 'Call Now',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _contactCategory!.type.getDisplayName(context),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _contactCategory!.type.getDefaultNote(context),
            style: TextStyle(
              fontSize: 15,
              color: Colors.white.withOpacity(0.8),
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSlots() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.schedule, size: 20, color: Colors.white.withOpacity(0.8)),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context)?.availableTimes ?? 'Available Times',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ..._contactCategory!.timeSlots.map((slot) => _buildTimeSlotCard(slot)),
        ],
      ),
    );
  }

  Widget _buildTimeSlotCard(TimeSlot slot) {
    final isActive = slot.isCurrentTimeInSlot();
    final isNotificationEnabled = _enabledNotifications.contains(slot.id);

    return TimeDisplayLTR(
      child: Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isActive ? Colors.green[50] : Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive ? Colors.green[200]! : Colors.white.withOpacity(0.2),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // Time slot info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: isActive ? Colors.green[100] : Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          slot.getDayName(context),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: isActive ? Colors.green[800] : Colors.white,
                          ),
                        ),
                      ),
                      if (isActive) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green[600],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            AppLocalizations.of(context)?.activeNowLabel ?? 'ACTIVE NOW',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    slot.timeRange,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isActive ? Colors.green[800] : Colors.white.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
            // Notification button
            Container(
              decoration: BoxDecoration(
                color: isNotificationEnabled ? Colors.orange[100] : Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isNotificationEnabled ? Colors.orange[300]! : Colors.white.withOpacity(0.3),
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () => _toggleNotification(slot.id),
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      children: [
                        Icon(
                          isNotificationEnabled ? Icons.notifications_active : Icons.notifications_none,
                          color: isNotificationEnabled ? Colors.orange[700] : Colors.white.withOpacity(0.6),
                          size: 20,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          isNotificationEnabled
                              ? (AppLocalizations.of(context)?.on ?? 'ON')
                              : (AppLocalizations.of(context)?.off ?? 'OFF'),
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: isNotificationEnabled ? Colors.orange[700] : Colors.white.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildNoPreferences() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(
            Icons.help_outline,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 12),
          Text(
            AppLocalizations.of(context)?.noPreferencesSet ?? 'No preferences set',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context)?.contactHasntSetPreferences ?? 'This contact hasn\'t set communication preferences for you yet.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(CategoryType type) {
    switch (type) {
      case CategoryType.contactAnytime:
        return Icons.access_time;
      case CategoryType.preferAnytime:
        return Icons.schedule;
      case CategoryType.contactAtTimes:
        return Icons.alarm;
      case CategoryType.contactThroughMessages:
        return Icons.message;
    }
  }



  Color _getCallingSuggestionColor() {
    if (_contactCategory == null) {
      return Colors.grey;
    }

    final isGoodTime = SupabaseService.isGoodTimeToContact(_contactCategory);

    return isGoodTime ? Colors.green : Colors.orange;
  }

  IconData _getCallingSuggestionIcon() {
    if (_contactCategory == null) {
      return Icons.help_outline;
    }

    final isGoodTime = SupabaseService.isGoodTimeToContact(_contactCategory);

    return isGoodTime ? Icons.check_circle : Icons.warning;
  }

  /// Build avatar image safely to prevent UI freezing
  Widget _buildSafeAvatar() {
    final avatarData = widget.contact.deviceContact.avatar;
    if (avatarData == null || avatarData.isEmpty) {
      return _buildFallbackAvatar();
    }

    return FutureBuilder<Uint8List?>(
      future: _decodeAvatarSafely(avatarData),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          if (snapshot.hasData && snapshot.data != null) {
            return Image.memory(
              snapshot.data!,
              width: 70,
              height: 70,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                print('Error displaying avatar image: $error');
                return _buildFallbackAvatar();
              },
            );
          }
        }
        // Show fallback while loading or on error
        return _buildFallbackAvatar();
      },
    );
  }

  /// Build fallback avatar with initials
  Widget _buildFallbackAvatar() {
    return Center(
      child: Text(
        widget.contact.displayName.isNotEmpty
            ? widget.contact.displayName[0].toUpperCase()
            : '?',
        style: const TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  /// Safely decode avatar data from various formats
  Future<Uint8List?> _decodeAvatarSafely(String avatarData) async {
    try {
      // Check if it's base64 encoded
      if (avatarData.startsWith('data:image/')) {
        // Remove data URL prefix if present
        final base64String = avatarData.split(',').last;
        return base64Decode(base64String);
      } else if (avatarData.length > 100 && !avatarData.contains('/')) {
        // Likely a base64 string without prefix
        return base64Decode(avatarData);
      } else {
        // Might be a file path or URL - not supported for now
        print('Unsupported avatar format: ${avatarData.substring(0, 50)}...');
        return null;
      }
    } catch (e) {
      print('Failed to decode avatar data: $e');
      return null;
    }
  }
}
