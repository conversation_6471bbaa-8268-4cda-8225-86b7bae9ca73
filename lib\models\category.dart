import 'package:flutter/material.dart';
import 'time_slot.dart';

enum CategoryType {
  contactAnytime(1, Icons.phone),
  preferAnytime(2, Icons.schedule),
  contactAtTimes(3, Icons.access_time),
  contactThroughMessages(4, Icons.message);

  const CategoryType(this.value, this.icon);
  final int value;
  final IconData icon;

  static CategoryType fromValue(int value) {
    return CategoryType.values.firstWhere((type) => type.value == value);
  }
}

class Category {
  final String id;
  final String userId;
  final CategoryType type;
  final String note;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<TimeSlot> timeSlots;

  Category({
    required this.id,
    required this.userId,
    required this.type,
    required this.note,
    required this.createdAt,
    required this.updatedAt,
    this.timeSlots = const [],
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      type: CategoryType.fromValue(json['type'] as int),
      note: json['note'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      timeSlots: (json['time_slots'] as List<dynamic>?)
              ?.map((slot) => TimeSlot.fromJson(slot as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'type': type.value,
      'note': note,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toJsonWithTimeSlots() {
    return {
      'id': id,
      'user_id': userId,
      'type': type.value,
      'note': note,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'time_slots': timeSlots.map((slot) => slot.toJson()).toList(),
    };
  }

  Category copyWith({
    String? id,
    String? userId,
    CategoryType? type,
    String? note,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<TimeSlot>? timeSlots,
  }) {
    return Category(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      note: note ?? this.note,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      timeSlots: timeSlots ?? this.timeSlots,
    );
  }

  bool get hasTimeSlots {
    return type == CategoryType.preferAnytime ||
        type == CategoryType.contactAtTimes ||
        type == CategoryType.contactThroughMessages;
  }

  @override
  String toString() {
    return 'Category(id: $id, type: ${type.name}, note: $note)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
