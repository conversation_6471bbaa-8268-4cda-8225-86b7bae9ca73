import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'lib/services/language_service.dart';

void main() {
  runApp(const LocalizationTestApp());
}

class LocalizationTestApp extends StatelessWidget {
  const LocalizationTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: LanguageService(),
      builder: (context, child) {
        return MaterialApp(
          title: 'Localization Test',
          locale: LanguageService().currentLocale,
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en'),
            Locale('ar'),
          ],
          home: const TestScreen(),
        );
      },
    );
  }
}

class TestScreen extends StatelessWidget {
  const TestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final languageService = LanguageService();
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n?.appTitle ?? 'Contact Times'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Language: ${languageService.currentLocale.languageCode}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              'Text Direction: ${languageService.isRTL ? 'RTL' : 'LTR'}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            Text(
              'Sample Translations:',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildTranslationRow('App Title', l10n?.appTitle ?? 'Contact Times'),
            _buildTranslationRow('App Subtitle', l10n?.appSubtitle ?? 'Manage when you can be contacted'),
            _buildTranslationRow('Sign In', l10n?.signIn ?? 'Sign In'),
            _buildTranslationRow('Sign Up', l10n?.signUp ?? 'Sign Up'),
            _buildTranslationRow('Email', l10n?.email ?? 'Email'),
            _buildTranslationRow('Password', l10n?.password ?? 'Password'),
            _buildTranslationRow('Contacts', l10n?.contacts ?? 'Contacts'),
            _buildTranslationRow('Categories', l10n?.categories ?? 'Categories'),
            _buildTranslationRow('Profile', l10n?.profile ?? 'Profile'),
            const SizedBox(height: 24),
            Row(
              children: [
                ElevatedButton(
                  onPressed: () => languageService.changeLanguage('en'),
                  child: const Text('English'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () => languageService.changeLanguage('ar'),
                  child: const Text('العربية'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildTranslationRow(String key, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$key:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
