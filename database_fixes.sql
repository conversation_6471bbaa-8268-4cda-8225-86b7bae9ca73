-- Database Fixes for Contact Times App - Enhanced Error Handling Version
-- Execute this file in your Supabase SQL editor to fix time slots and database issues
-- Date: 2025-01-05
-- Version: 2.0 - Enhanced with comprehensive error handling and debugging

-- Enable detailed logging
SET log_statement = 'all';
SET log_min_messages = 'notice';

-- =============================================================================
-- SECTION 1: Fix Time Slots Table Structure
-- =============================================================================

-- Ensure time_slots table has proper structure and constraints
-- This will recreate the table if there are any structural issues

-- First, let's check if we need to update the time_slots table structure
DO $$
BEGIN
    -- Check if time_slots table exists and has correct structure
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'time_slots' AND table_schema = 'public') THEN
        -- Check if the table has the correct columns
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'time_slots' 
            AND table_schema = 'public' 
            AND column_name = 'created_at'
        ) THEN
            -- Add missing created_at column if it doesn't exist
            ALTER TABLE public.time_slots ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        END IF;
        
        -- Ensure proper constraints exist
        ALTER TABLE public.time_slots DROP CONSTRAINT IF EXISTS time_slots_day_of_week_check;
        ALTER TABLE public.time_slots ADD CONSTRAINT time_slots_day_of_week_check 
            CHECK (day_of_week >= 0 AND day_of_week <= 6);
            
        RAISE NOTICE 'Time slots table structure verified and updated';
    ELSE
        RAISE NOTICE 'Time slots table does not exist - please run the main schema first';
    END IF;
END $$;

-- =============================================================================
-- SECTION 2: Clean Up Existing Data Issues
-- =============================================================================

-- Remove any time slots with invalid IDs or data
DELETE FROM public.time_slots 
WHERE id IS NULL 
   OR category_id IS NULL 
   OR day_of_week IS NULL 
   OR day_of_week < 0 
   OR day_of_week > 6
   OR start_time IS NULL 
   OR end_time IS NULL;

-- Remove orphaned time slots (where category doesn't exist)
DELETE FROM public.time_slots 
WHERE NOT EXISTS (
    SELECT 1 FROM public.categories 
    WHERE categories.id = time_slots.category_id
);

-- =============================================================================
-- SECTION 3: Update RLS Policies for Better Performance
-- =============================================================================

-- Drop existing time slots policies to recreate them with better performance
DROP POLICY IF EXISTS "Users can view time slots for their categories" ON public.time_slots;
DROP POLICY IF EXISTS "Users can view time slots for categories assigned to them" ON public.time_slots;
DROP POLICY IF EXISTS "Users can insert time slots for their categories" ON public.time_slots;
DROP POLICY IF EXISTS "Users can update time slots for their categories" ON public.time_slots;
DROP POLICY IF EXISTS "Users can delete time slots for their categories" ON public.time_slots;

-- Recreate time slots policies with optimized queries
CREATE POLICY "Users can view time slots for their categories" ON public.time_slots
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.categories
            WHERE categories.id = time_slots.category_id
            AND categories.user_id = auth.uid()
        )
    );

-- Allow users to view time slots for categories that were assigned to them
CREATE POLICY "Users can view time slots for categories assigned to them" ON public.time_slots
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.categories
            JOIN public.user_contacts ON user_contacts.assigned_category_id = categories.id
            JOIN public.profiles ON profiles.id = auth.uid()
            WHERE categories.id = time_slots.category_id
            AND (
                -- Direct phone number match
                profiles.phone_number = user_contacts.categorized_contact_phone
                OR
                -- Match without + prefix
                REPLACE(profiles.phone_number, '+', '') = user_contacts.categorized_contact_phone
                OR
                -- Match normalized versions (remove country code and + prefix)
                CASE 
                    WHEN profiles.phone_number LIKE '+967%' THEN SUBSTRING(profiles.phone_number FROM 5)
                    WHEN profiles.phone_number LIKE '967%' THEN SUBSTRING(profiles.phone_number FROM 4)
                    ELSE profiles.phone_number
                END = user_contacts.categorized_contact_phone
            )
        )
    );

CREATE POLICY "Users can insert time slots for their categories" ON public.time_slots
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.categories 
            WHERE categories.id = time_slots.category_id 
            AND categories.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update time slots for their categories" ON public.time_slots
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.categories 
            WHERE categories.id = time_slots.category_id 
            AND categories.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete time slots for their categories" ON public.time_slots
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.categories 
            WHERE categories.id = time_slots.category_id 
            AND categories.user_id = auth.uid()
        )
    );

-- =============================================================================
-- SECTION 4: Add Helpful Functions for Time Slots Management
-- =============================================================================

-- Function to clean up orphaned time slots
CREATE OR REPLACE FUNCTION cleanup_orphaned_time_slots()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.time_slots 
    WHERE NOT EXISTS (
        SELECT 1 FROM public.categories 
        WHERE categories.id = time_slots.category_id
    );
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate time slot data
CREATE OR REPLACE FUNCTION validate_time_slot()
RETURNS TRIGGER AS $$
BEGIN
    -- Ensure day_of_week is valid
    IF NEW.day_of_week < 0 OR NEW.day_of_week > 6 THEN
        RAISE EXCEPTION 'day_of_week must be between 0 and 6, got %', NEW.day_of_week;
    END IF;
    
    -- Ensure start_time is before end_time
    IF NEW.start_time >= NEW.end_time THEN
        RAISE EXCEPTION 'start_time must be before end_time';
    END IF;
    
    -- Ensure category exists and belongs to the user
    IF NOT EXISTS (
        SELECT 1 FROM public.categories 
        WHERE id = NEW.category_id 
        AND user_id = auth.uid()
    ) THEN
        RAISE EXCEPTION 'Category does not exist or does not belong to the current user';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for time slot validation
DROP TRIGGER IF EXISTS validate_time_slot_trigger ON public.time_slots;
CREATE TRIGGER validate_time_slot_trigger
    BEFORE INSERT OR UPDATE ON public.time_slots
    FOR EACH ROW
    EXECUTE FUNCTION validate_time_slot();

-- =============================================================================
-- SECTION 5: Add Indexes for Better Performance
-- =============================================================================

-- Create additional indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_time_slots_category_day ON public.time_slots(category_id, day_of_week);
CREATE INDEX IF NOT EXISTS idx_time_slots_times ON public.time_slots(start_time, end_time);
CREATE INDEX IF NOT EXISTS idx_categories_user_type ON public.categories(user_id, type);

-- =============================================================================
-- SECTION 6: Update Categories Table for Better Time Slots Support
-- =============================================================================

-- Ensure categories table has proper updated_at handling
CREATE OR REPLACE FUNCTION update_category_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger for categories updated_at
DROP TRIGGER IF EXISTS update_categories_updated_at ON public.categories;
CREATE TRIGGER update_categories_updated_at
    BEFORE UPDATE ON public.categories
    FOR EACH ROW
    EXECUTE FUNCTION update_category_updated_at();

-- =============================================================================
-- SECTION 7: Enhanced Debugging and Error Handling Functions
-- =============================================================================

-- Function to debug time slots issues
CREATE OR REPLACE FUNCTION debug_time_slots_issues()
RETURNS TABLE(
    issue_type TEXT,
    issue_description TEXT,
    affected_count BIGINT,
    sample_data JSONB
) AS $$
BEGIN
    -- Check for time slots with invalid UUIDs
    RETURN QUERY
    SELECT
        'invalid_uuid'::TEXT,
        'Time slots with invalid UUID format'::TEXT,
        COUNT(*)::BIGINT,
        jsonb_agg(jsonb_build_object('id', id, 'category_id', category_id) ORDER BY created_at DESC LIMIT 3)
    FROM public.time_slots
    WHERE id !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    HAVING COUNT(*) > 0;

    -- Check for orphaned time slots
    RETURN QUERY
    SELECT
        'orphaned_slots'::TEXT,
        'Time slots without valid category'::TEXT,
        COUNT(*)::BIGINT,
        jsonb_agg(jsonb_build_object('id', id, 'category_id', category_id) ORDER BY created_at DESC LIMIT 3)
    FROM public.time_slots ts
    WHERE NOT EXISTS (SELECT 1 FROM public.categories c WHERE c.id = ts.category_id)
    HAVING COUNT(*) > 0;

    -- Check for time slots with invalid day_of_week
    RETURN QUERY
    SELECT
        'invalid_day'::TEXT,
        'Time slots with invalid day_of_week'::TEXT,
        COUNT(*)::BIGINT,
        jsonb_agg(jsonb_build_object('id', id, 'day_of_week', day_of_week) ORDER BY created_at DESC LIMIT 3)
    FROM public.time_slots
    WHERE day_of_week < 0 OR day_of_week > 6
    HAVING COUNT(*) > 0;

    -- Check for time slots with invalid time ranges
    RETURN QUERY
    SELECT
        'invalid_time_range'::TEXT,
        'Time slots where start_time >= end_time'::TEXT,
        COUNT(*)::BIGINT,
        jsonb_agg(jsonb_build_object('id', id, 'start_time', start_time, 'end_time', end_time) ORDER BY created_at DESC LIMIT 3)
    FROM public.time_slots
    WHERE start_time >= end_time
    HAVING COUNT(*) > 0;

    -- Check for categories without proper user ownership
    RETURN QUERY
    SELECT
        'invalid_category_ownership'::TEXT,
        'Categories with invalid user_id'::TEXT,
        COUNT(*)::BIGINT,
        jsonb_agg(jsonb_build_object('id', id, 'user_id', user_id, 'type', type) ORDER BY created_at DESC LIMIT 3)
    FROM public.categories
    WHERE user_id IS NULL OR NOT EXISTS (SELECT 1 FROM auth.users WHERE id = categories.user_id)
    HAVING COUNT(*) > 0;

    RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to test RLS policies
CREATE OR REPLACE FUNCTION test_rls_policies(test_user_id UUID DEFAULT NULL)
RETURNS TABLE(
    policy_name TEXT,
    operation TEXT,
    test_result TEXT,
    error_message TEXT
) AS $$
DECLARE
    current_user_id UUID;
    test_category_id UUID;
    test_time_slot_id UUID;
BEGIN
    -- Use provided user ID or current user
    current_user_id := COALESCE(test_user_id, auth.uid());

    IF current_user_id IS NULL THEN
        RETURN QUERY SELECT 'auth_check'::TEXT, 'authentication'::TEXT, 'FAILED'::TEXT, 'No authenticated user'::TEXT;
        RETURN;
    END IF;

    -- Test category access
    BEGIN
        SELECT id INTO test_category_id FROM public.categories WHERE user_id = current_user_id LIMIT 1;

        IF test_category_id IS NULL THEN
            RETURN QUERY SELECT 'category_access'::TEXT, 'SELECT'::TEXT, 'NO_DATA'::TEXT, 'No categories found for user'::TEXT;
        ELSE
            RETURN QUERY SELECT 'category_access'::TEXT, 'SELECT'::TEXT, 'SUCCESS'::TEXT, 'Found category: ' || test_category_id::TEXT;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT 'category_access'::TEXT, 'SELECT'::TEXT, 'ERROR'::TEXT, SQLERRM;
    END;

    -- Test time slot access
    IF test_category_id IS NOT NULL THEN
        BEGIN
            SELECT id INTO test_time_slot_id FROM public.time_slots WHERE category_id = test_category_id LIMIT 1;

            IF test_time_slot_id IS NULL THEN
                RETURN QUERY SELECT 'time_slot_access'::TEXT, 'SELECT'::TEXT, 'NO_DATA'::TEXT, 'No time slots found for category'::TEXT;
            ELSE
                RETURN QUERY SELECT 'time_slot_access'::TEXT, 'SELECT'::TEXT, 'SUCCESS'::TEXT, 'Found time slot: ' || test_time_slot_id::TEXT;
            END IF;
        EXCEPTION WHEN OTHERS THEN
            RETURN QUERY SELECT 'time_slot_access'::TEXT, 'SELECT'::TEXT, 'ERROR'::TEXT, SQLERRM;
        END;
    END IF;

    RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log detailed operation attempts
CREATE OR REPLACE FUNCTION log_operation_attempt(
    operation_type TEXT,
    table_name TEXT,
    operation_data JSONB,
    user_id UUID DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO public.operation_logs (
        operation_type,
        table_name,
        operation_data,
        user_id,
        attempted_at
    ) VALUES (
        operation_type,
        table_name,
        operation_data,
        COALESCE(user_id, auth.uid()),
        NOW()
    );
EXCEPTION WHEN OTHERS THEN
    -- If logging fails, at least raise a notice
    RAISE NOTICE 'Failed to log operation: % on % - %', operation_type, table_name, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create operation logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.operation_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    operation_type TEXT NOT NULL,
    table_name TEXT NOT NULL,
    operation_data JSONB,
    user_id UUID,
    attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT
);

-- Enable RLS on operation logs
ALTER TABLE public.operation_logs ENABLE ROW LEVEL SECURITY;

-- Policy for operation logs (users can only see their own logs)
DROP POLICY IF EXISTS "Users can view their own operation logs" ON public.operation_logs;
CREATE POLICY "Users can view their own operation logs" ON public.operation_logs
    FOR SELECT USING (user_id = auth.uid());

DROP POLICY IF EXISTS "Users can insert their own operation logs" ON public.operation_logs;
CREATE POLICY "Users can insert their own operation logs" ON public.operation_logs
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- =============================================================================
-- SECTION 8: Verification and Cleanup
-- =============================================================================

-- Clean up any orphaned time slots
SELECT cleanup_orphaned_time_slots() as orphaned_time_slots_cleaned;

-- Run comprehensive debugging
RAISE NOTICE '🔍 Running comprehensive database debugging...';

-- Check for data issues
DO $$
DECLARE
    issue_record RECORD;
    issue_count INTEGER := 0;
BEGIN
    RAISE NOTICE '📊 Checking for data integrity issues...';

    FOR issue_record IN SELECT * FROM debug_time_slots_issues() LOOP
        issue_count := issue_count + 1;
        RAISE WARNING '⚠️ Issue found: % - % (Count: %)',
            issue_record.issue_type,
            issue_record.issue_description,
            issue_record.affected_count;
        RAISE NOTICE 'Sample data: %', issue_record.sample_data;
    END LOOP;

    IF issue_count = 0 THEN
        RAISE NOTICE '✅ No data integrity issues found';
    ELSE
        RAISE WARNING '⚠️ Found % data integrity issues that need attention', issue_count;
    END IF;
END $$;

-- Test RLS policies
DO $$
DECLARE
    policy_record RECORD;
    policy_test_count INTEGER := 0;
    policy_success_count INTEGER := 0;
BEGIN
    RAISE NOTICE '🔐 Testing RLS policies...';

    FOR policy_record IN SELECT * FROM test_rls_policies() LOOP
        policy_test_count := policy_test_count + 1;

        IF policy_record.test_result = 'SUCCESS' THEN
            policy_success_count := policy_success_count + 1;
            RAISE NOTICE '✅ %: % - %', policy_record.policy_name, policy_record.operation, policy_record.test_result;
        ELSE
            RAISE WARNING '❌ %: % - % (%)',
                policy_record.policy_name,
                policy_record.operation,
                policy_record.test_result,
                policy_record.error_message;
        END IF;
    END LOOP;

    RAISE NOTICE 'Policy tests: %/% passed', policy_success_count, policy_test_count;
END $$;

-- Verify the fixes
DO $$
DECLARE
    category_count INTEGER;
    time_slot_count INTEGER;
    policy_count INTEGER;
    user_count INTEGER;
    auth_user_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO category_count FROM public.categories;
    SELECT COUNT(*) INTO time_slot_count FROM public.time_slots;
    SELECT COUNT(*) INTO policy_count FROM pg_policies WHERE tablename = 'time_slots';
    SELECT COUNT(*) INTO user_count FROM public.profiles;
    SELECT COUNT(*) INTO auth_user_count FROM auth.users;

    RAISE NOTICE '📈 Database statistics:';
    RAISE NOTICE '- Auth users: %', auth_user_count;
    RAISE NOTICE '- Profile users: %', user_count;
    RAISE NOTICE '- Categories: %', category_count;
    RAISE NOTICE '- Time slots: %', time_slot_count;
    RAISE NOTICE '- Time slots policies: %', policy_count;

    IF policy_count >= 5 THEN
        RAISE NOTICE '✅ All time slots policies are in place';
    ELSE
        RAISE WARNING '⚠️ Some time slots policies may be missing (expected: 5, found: %)', policy_count;
    END IF;

    -- Check for potential issues
    IF auth_user_count = 0 THEN
        RAISE WARNING '⚠️ No authenticated users found - this may cause RLS issues';
    END IF;

    IF category_count = 0 THEN
        RAISE WARNING '⚠️ No categories found - users may need to create categories first';
    END IF;
END $$;

-- =============================================================================
-- COMPLETION MESSAGE
-- =============================================================================

DO $$
BEGIN
    RAISE NOTICE '🎉 Database fixes completed successfully!';
    RAISE NOTICE '';
    RAISE NOTICE 'Applied fixes:';
    RAISE NOTICE '✅ Time slots table structure verified';
    RAISE NOTICE '✅ Invalid data cleaned up';
    RAISE NOTICE '✅ RLS policies updated with phone number normalization';
    RAISE NOTICE '✅ Performance indexes added';
    RAISE NOTICE '✅ Validation functions created';
    RAISE NOTICE '✅ Triggers updated';
    RAISE NOTICE '';
    RAISE NOTICE 'Your app should now properly save and sync time slots!';
END $$;
