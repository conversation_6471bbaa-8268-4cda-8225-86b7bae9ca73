// ignore_for_file: avoid_print

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'connectivity_service.dart';
import 'contacts_service.dart';
import 'local_database_service.dart';
import 'supabase_service.dart';
import 'sync_service.dart';
import 'performance_monitor_service.dart';
import '../models/models.dart' as models;

class OfflineContactService extends ChangeNotifier {
  static final OfflineContactService _instance = OfflineContactService._internal();
  factory OfflineContactService() => _instance;
  OfflineContactService._internal();

  // Flag to prevent multiple simultaneous syncs
  bool _isSyncing = false;

  final ConnectivityService _connectivityService = ConnectivityService();
  final SyncService _syncService = SyncService();
  final PerformanceMonitorService _performanceMonitor = PerformanceMonitorService();

  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  List<models.Profile> _cachedProfiles = [];
  List<models.Profile> get cachedProfiles => _cachedProfiles;

  Map<String, models.Category> _cachedCategories = {};
  Map<String, models.Category> get cachedCategories => _cachedCategories;

  DateTime? _lastConnectivityChange;
  DateTime? _lastFullSync;
  DateTime? _lastIncrementalSync;

  // Smart caching flags
  bool _profilesCacheValid = false;
  bool _categoriesCacheValid = false;
  bool _assignmentsCacheValid = false;

  // Stream controllers for real-time data updates
  final StreamController<List<models.Profile>> _profilesStreamController =
      StreamController<List<models.Profile>>.broadcast();
  final StreamController<List<models.Category>> _categoriesStreamController =
      StreamController<List<models.Category>>.broadcast();
  final StreamController<Map<String, models.Category?>> _contactAssignmentsStreamController =
      StreamController<Map<String, models.Category?>>.broadcast();
  final StreamController<bool> _syncStatusStreamController =
      StreamController<bool>.broadcast();

  // Public streams for UI consumption
  Stream<List<models.Profile>> get profilesStream => _profilesStreamController.stream;
  Stream<List<models.Category>> get categoriesStream => _categoriesStreamController.stream;
  Stream<Map<String, models.Category?>> get contactAssignmentsStream => _contactAssignmentsStreamController.stream;
  Stream<bool> get syncStatusStream => _syncStatusStreamController.stream;

  // Cache for contact assignments to avoid repeated database queries
  Map<String, models.Category?> _contactAssignmentsCache = {};



  Future<void> initialize() async {
    if (_isInitialized) return;

    print('📱 Initializing OfflineContactService...');

    try {
      // Clean up any duplicate categories first
      await LocalDatabaseService.cleanupDuplicateCategories();

      // Initialize connectivity and sync services
      await _connectivityService.initialize();
      await _syncService.initialize();

      // Load cached data
      await _loadCachedData();

      // Listen for connectivity changes to track timing
      _connectivityService.addListener(_onConnectivityChanged);

      // Debug database contents
      await LocalDatabaseService.debugDatabaseContents();

      _isInitialized = true;
      print('✅ OfflineContactService initialized successfully');
    } catch (e) {
      print('❌ Failed to initialize OfflineContactService: $e');
      rethrow;
    }
  }

  void _onConnectivityChanged() {
    // Track when connectivity changes to prevent immediate server sync
    _lastConnectivityChange = DateTime.now();
    print('📱 Connectivity changed at ${_lastConnectivityChange}');
  }

  Future<void> _loadCachedData() async {
    try {
      print('📱 Loading cached data...');

      // Load cached profiles
      final profiles = await LocalDatabaseService.getAllProfiles();
      print('📱 Loaded ${profiles.length} cached profiles');

      // Store profiles directly for offline use
      _cachedProfiles = profiles;

      // Emit profiles to stream
      _profilesStreamController.add(_cachedProfiles);

      // Load cached categories for current user
      final currentUser = SupabaseService.currentUser;
      if (currentUser != null) {
        final categories = await LocalDatabaseService.getCategoriesByUserId(currentUser.id);
        _cachedCategories = {
          for (final category in categories) category.id: category
        };
        print('📱 Loaded ${categories.length} cached categories');

        // Emit categories to stream
        _categoriesStreamController.add(_cachedCategories.values.toList());

        // Load and emit contact assignments
        await _loadContactAssignments(currentUser.id);
      }

      notifyListeners();
    } catch (e) {
      print('❌ Failed to load cached data: $e');
    }
  }

  Future<void> _loadContactAssignments(String userId) async {
    try {
      print('📱 Loading contact assignments...');

      // Get all user contact assignments from local storage
      final db = await LocalDatabaseService.database;
      final userContactMaps = await db.query(
        'user_contacts',
        where: 'user_id = ?',
        whereArgs: [userId],
      );

      // Build new assignments map from database
      Map<String, models.Category?> newAssignments = {};
      for (final map in userContactMaps) {
        final categoryId = map['assigned_category_id'] as String;
        final contactPhone = map['categorized_contact_phone'] as String;
        final category = _cachedCategories[categoryId];

        if (category != null) {
          newAssignments[contactPhone] = category;

          // Also cache phone number variations for faster lookup
          final phoneVariations = ContactsService.generatePhoneVariations(contactPhone);
          for (final variation in phoneVariations) {
            if (variation != contactPhone) {
              newAssignments[variation] = category;
            }
          }
        }
      }

      // Merge with existing cache instead of clearing it completely
      // This preserves recent assignments that might not be in database yet
      for (final entry in newAssignments.entries) {
        _contactAssignmentsCache[entry.key] = entry.value;
      }

      print('📱 Loaded ${userContactMaps.length} contact assignments (merged with existing cache)');

      // Emit contact assignments to stream
      _contactAssignmentsStreamController.add(Map.from(_contactAssignmentsCache));
    } catch (e) {
      print('❌ Failed to load contact assignments: $e');
    }
  }

  Future<void> _cleanupDuplicateCategories() async {
    try {
      print('🧹 Cleaning up duplicate categories...');

      final currentUser = SupabaseService.currentUser;
      if (currentUser == null) return;

      // Get all categories from database
      final allCategories = await LocalDatabaseService.getCategoriesByUserId(currentUser.id);

      // Group by type to find duplicates
      final categoryGroups = <models.CategoryType, List<models.Category>>{};
      for (final category in allCategories) {
        categoryGroups.putIfAbsent(category.type, () => []).add(category);
      }

      // Remove duplicates (keep the most recent one)
      for (final entry in categoryGroups.entries) {
        final categories = entry.value;
        if (categories.length > 1) {
          print('🧹 Found ${categories.length} duplicate categories of type: ${entry.key.name}');

          // Sort by creation date (newest first)
          categories.sort((a, b) => b.createdAt.compareTo(a.createdAt));

          // Keep the first (newest) and remove the rest
          for (int i = 1; i < categories.length; i++) {
            print('🧹 Removing duplicate category: ${categories[i].id}');
            // For now, just clear from cache - we'll implement deleteCategory later if needed
            _cachedCategories.remove(categories[i].id);
          }
        }
      }

      print('✅ Duplicate category cleanup completed');
    } catch (e) {
      print('❌ Failed to cleanup duplicate categories: $e');
    }
  }

  Future<void> syncWithServer() async {
    if (!_connectivityService.isOnline) {
      print('📱 Cannot sync - device is offline');
      return;
    }

    try {
      print('📱 Syncing with server...');
      
      final currentUser = SupabaseService.currentUser;
      if (currentUser == null) {
        print('📱 No current user - skipping sync');
        return;
      }

      // Sync categories
      await _syncCategories(currentUser.id);

      // Sync contacts (profiles)
      await _syncProfiles();

      // Sync user contacts (category assignments)
      await _syncUserContacts(currentUser.id);

      // Sync notification preferences
      await _syncNotificationPreferences(currentUser.id);

      // Trigger sync service for pending operations
      await _syncService.syncPendingOperations();

      // After syncing, reload cached data to ensure consistency
      await _loadCachedData();

      print('✅ Sync with server completed');
    } catch (e) {
      print('❌ Failed to sync with server: $e');
    }
  }

  // Incremental sync - only fetch data that changed since last sync
  Future<void> incrementalSync(DateTime? lastSyncTime) async {
    if (!_connectivityService.isOnline) {
      print('📱 Cannot sync - device is offline');
      return;
    }

    try {
      final currentUser = SupabaseService.currentUser;
      if (currentUser == null) {
        print('📱 No current user - skipping incremental sync');
        return;
      }

      print('🔄 Starting incremental sync...');
      print('🔄 Last sync time: ${lastSyncTime?.toIso8601String() ?? 'never'}');

      // Only sync data that changed since last sync
      await _incrementalSyncCategories(currentUser.id, lastSyncTime);
      await _syncProfiles(); // Always sync profiles to ensure they're cached
      await _incrementalSyncUserContacts(currentUser.id, lastSyncTime);

      // Trigger sync service for pending operations
      await _syncService.syncPendingOperations();

      print('✅ Incremental sync completed');
    } catch (e) {
      print('❌ Incremental sync failed: $e');
    }
  }

  Future<void> _syncCategories(String userId) async {
    if (_isSyncing) {
      print('📱 Category sync already in progress, skipping...');
      return;
    }

    _isSyncing = true;
    try {
      print('🔄 Syncing categories from server for user: $userId');
      // Fetch latest categories from server
      final serverCategories = await SupabaseService.getUserCategories(userId);

      print('📱 Server returned ${serverCategories.length} categories');
      for (final category in serverCategories) {
        print('📱   Category: ${category.type.name} (ID: ${category.id}) with ${category.timeSlots.length} time slots');
        for (final slot in category.timeSlots) {
          print('📱     - ${slot.dayName}: ${slot.timeRange}');
        }
      }

      // Clear existing cache to prevent duplicates
      print('📱 Clearing existing category cache...');
      _cachedCategories.clear();

      // Update local database and cache
      for (final category in serverCategories) {
        print('📱 Inserting category ${category.id} with ${category.timeSlots.length} time slots into local database');
        await LocalDatabaseService.insertCategory(category);
        _cachedCategories[category.id] = category;
      }

      print('📱 Synced ${serverCategories.length} categories');

      // Debug database contents after sync
      await LocalDatabaseService.debugDatabaseContents();
    } catch (e) {
      print('❌ Failed to sync categories: $e');
    } finally {
      _isSyncing = false;
    }
  }

  Future<void> _syncProfiles() async {
    try {
      print('📱 Syncing profiles from server...');

      // Fetch all profiles from server
      final response = await SupabaseService.client
          .from('profiles')
          .select();

      final serverProfiles = response.map<models.Profile>((json) => models.Profile.fromJson(json)).toList();
      print('📱 Fetched ${serverProfiles.length} profiles from server');

      // Cache profiles in local database
      for (final profile in serverProfiles) {
        await LocalDatabaseService.insertProfile(profile);
      }

      // Update in-memory cache
      _cachedProfiles = serverProfiles;

      print('📱 Synced ${serverProfiles.length} profiles');
    } catch (e) {
      print('❌ Failed to sync profiles: $e');
    }
  }

  Future<void> _syncUserContacts(String userId) async {
    try {
      // Get current user's profile to get their phone number
      final currentProfile = await getCurrentProfile();
      if (currentProfile == null) {
        print('❌ Cannot sync user contacts - current profile not found');
        return;
      }

      // Fetch latest user contacts from server (where current user did the categorizing)
      final serverUserContacts = await SupabaseService.getUserContacts(userId);

      // Also fetch user contacts where OTHER users categorized the CURRENT user
      // This is needed for the reverse lookup in getContactCategoryOffline
      print('📱 Fetching reverse user contacts for phone: ${currentProfile.phoneNumber}');
      final reverseUserContacts = await SupabaseService.getUserContactsByCategorizedPhone(
        currentProfile.phoneNumber,
      );
      print('📱 Found ${reverseUserContacts.length} reverse user contacts');

      // Update local database
      final db = await LocalDatabaseService.database;

      // Get unsynced offline assignments before clearing
      final unsyncedAssignments = await db.query(
        'user_contacts',
        where: 'last_synced IS NULL',
      );
      print('📱 Found ${unsyncedAssignments.length} unsynced offline assignments to preserve');

      // Clear only synced user contacts to avoid deleting offline changes
      await db.delete('user_contacts', where: 'last_synced IS NOT NULL');

      // Insert user contacts where current user did the categorizing
      for (final userContact in serverUserContacts) {
        // Check if there's an unsynced local version first
        final existingUnsynced = await db.query(
          'user_contacts',
          where: 'user_id = ? AND categorized_contact_phone = ? AND last_synced IS NULL',
          whereArgs: [userContact.userId, userContact.categorizedContactPhone],
        );

        if (existingUnsynced.isEmpty) {
          // No unsynced local version, safe to insert server version
          await db.insert(
            'user_contacts',
            {
              'id': userContact.id,
              'user_id': userContact.userId,
              'categorized_contact_phone': userContact.categorizedContactPhone,
              'assigned_category_id': userContact.assignedCategoryId,
              'created_at': userContact.createdAt.toIso8601String(),
              'updated_at': userContact.updatedAt.toIso8601String(),
              'last_synced': DateTime.now().toIso8601String(),
            },
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        } else {
          print('📱 Preserving unsynced local assignment for ${userContact.categorizedContactPhone}');
        }

        // Also sync the category for this user contact if we don't have it
        if (userContact.category != null) {
          await LocalDatabaseService.insertCategory(userContact.category!);
          _cachedCategories[userContact.assignedCategoryId] = userContact.category!;
          print('📱 Synced category from user contact: ${userContact.category!.type.name}');
        }
      }

      // Insert user contacts where other users categorized the current user
      for (final userContact in reverseUserContacts) {
        // Check if there's an unsynced local version first
        final existingUnsynced = await db.query(
          'user_contacts',
          where: 'user_id = ? AND categorized_contact_phone = ? AND last_synced IS NULL',
          whereArgs: [userContact.userId, userContact.categorizedContactPhone],
        );

        if (existingUnsynced.isEmpty) {
          // No unsynced local version, safe to insert server version
          await db.insert(
            'user_contacts',
            {
              'id': userContact.id,
              'user_id': userContact.userId,
              'categorized_contact_phone': userContact.categorizedContactPhone,
              'assigned_category_id': userContact.assignedCategoryId,
              'created_at': userContact.createdAt.toIso8601String(),
              'updated_at': userContact.updatedAt.toIso8601String(),
              'last_synced': DateTime.now().toIso8601String(),
            },
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        } else {
          print('📱 Preserving unsynced reverse assignment for ${userContact.categorizedContactPhone}');
        }

        // Also sync the category for this user contact if we don't have it
        print('📱 Checking reverse user contact category: ${userContact.assignedCategoryId}');
        print('📱 Category is null: ${userContact.category == null}');
        print('📱 Already cached: ${_cachedCategories.containsKey(userContact.assignedCategoryId)}');

        if (userContact.category != null) {
          // Always sync the category, even if we think we have it cached
          await LocalDatabaseService.insertCategory(userContact.category!);
          _cachedCategories[userContact.assignedCategoryId] = userContact.category!;
          print('📱 ✅ Synced category from reverse user contact: ${userContact.category!.type.name} (ID: ${userContact.assignedCategoryId})');
        } else if (userContact.category == null) {
          print('📱 ❌ Category is null for reverse user contact: ${userContact.assignedCategoryId}');
          // Try to fetch the category separately if it's missing
          try {
            final category = await SupabaseService.getCategoryById(userContact.assignedCategoryId);
            if (category != null) {
              await LocalDatabaseService.insertCategory(category);
              _cachedCategories[userContact.assignedCategoryId] = category;
              print('📱 ✅ Fetched and synced missing category: ${category.type.name} (ID: ${userContact.assignedCategoryId})');
            } else {
              print('📱 ❌ Could not fetch category with ID: ${userContact.assignedCategoryId}');
            }
          } catch (e) {
            print('📱 ❌ Error fetching missing category: $e');
          }
        }
      }

      print('📱 Synced ${serverUserContacts.length} user contacts (current user as categorizer)');
      print('📱 Synced ${reverseUserContacts.length} user contacts (current user as categorized)');

      // Sync all categories referenced by user_contacts that we don't have locally
      await _syncMissingCategories();
    } catch (e) {
      print('❌ Failed to sync user contacts: $e');
    }
  }

  // Sync categories that are referenced by user_contacts but missing from local database
  Future<void> _syncMissingCategories() async {
    try {
      print('📱 Syncing missing categories...');

      // Get all category IDs referenced by user_contacts
      final db = await LocalDatabaseService.database;
      final result = await db.rawQuery('''
        SELECT DISTINCT assigned_category_id
        FROM user_contacts
        WHERE assigned_category_id IS NOT NULL
      ''');

      final referencedCategoryIds = result.map((row) => row['assigned_category_id'] as String).toSet();
      print('📱 Found ${referencedCategoryIds.length} referenced category IDs');

      // Check which categories are missing from local database
      final missingCategoryIds = <String>[];
      for (final categoryId in referencedCategoryIds) {
        final existingCategory = await LocalDatabaseService.getCategoryById(categoryId);
        if (existingCategory == null) {
          missingCategoryIds.add(categoryId);
        }
      }

      print('📱 Found ${missingCategoryIds.length} missing categories');

      // Fetch and sync missing categories
      for (final categoryId in missingCategoryIds) {
        try {
          final category = await SupabaseService.getCategoryById(categoryId);
          if (category != null) {
            await LocalDatabaseService.insertCategory(category);
            _cachedCategories[categoryId] = category;
            print('📱 ✅ Synced missing category: ${category.type.name} (ID: $categoryId)');
          } else {
            print('📱 ❌ Could not fetch category with ID: $categoryId');
          }
        } catch (e) {
          print('📱 ❌ Error fetching category $categoryId: $e');
        }
      }

      print('📱 Missing categories sync completed');
    } catch (e) {
      print('❌ Failed to sync missing categories: $e');
    }
  }

  // Incremental sync for categories - only fetch changed data
  Future<void> _incrementalSyncCategories(String userId, DateTime? lastSyncTime) async {
    try {
      print('🔄 Incremental sync categories...');

      if (lastSyncTime == null) {
        // First time sync - get all categories
        await _syncCategories(userId);
        return;
      }

      // For now, we'll do a simple check - in a real app you'd use server timestamps
      // This is a simplified version that checks if we have any categories cached
      final cachedCount = _cachedCategories.length;
      final serverCategories = await SupabaseService.getUserCategories(userId);

      if (serverCategories.length != cachedCount) {
        print('🔄 Categories changed (${cachedCount} -> ${serverCategories.length}), updating...');

        // Update only changed categories
        for (final category in serverCategories) {
          final existingCategory = _cachedCategories[category.id];
          if (existingCategory == null ||
              existingCategory.updatedAt.isBefore(category.updatedAt)) {
            await LocalDatabaseService.insertCategory(category);
            _cachedCategories[category.id] = category;
            print('🔄 Updated category: ${category.type.name}');
          }
        }

        print('✅ Incremental categories sync completed');
      } else {
        print('✅ Categories up to date');
      }
    } catch (e) {
      print('❌ Failed to incremental sync categories: $e');
    }
  }

  // Incremental sync for user contacts - only fetch changed data
  Future<void> _incrementalSyncUserContacts(String userId, DateTime? lastSyncTime) async {
    try {
      print('🔄 Incremental sync user contacts...');

      if (lastSyncTime == null) {
        // First time sync - get all user contacts
        await _syncUserContacts(userId);
        return;
      }

      // Get current cached count (all user_contacts, not just for current user)
      final db = await LocalDatabaseService.database;
      final cachedResult = await db.query('user_contacts');
      final cachedCount = cachedResult.length;

      // Get server count for both directions
      final serverUserContacts = await SupabaseService.getUserContacts(userId);

      final currentProfile = await getCurrentProfile();
      final reverseUserContacts = currentProfile != null
          ? await SupabaseService.getUserContactsByCategorizedPhone(currentProfile.phoneNumber)
          : <models.UserContact>[];

      final totalServerCount = serverUserContacts.length + reverseUserContacts.length;

      if (totalServerCount != cachedCount) {
        print('🔄 User contacts changed (${cachedCount} -> ${totalServerCount}), updating...');

        // For simplicity, we'll do a full refresh if counts differ
        // In a real app, you'd compare timestamps and update only changed records
        await _syncUserContacts(userId);

        print('✅ Incremental user contacts sync completed');
      } else {
        print('✅ User contacts up to date');
      }
    } catch (e) {
      print('❌ Failed to incremental sync user contacts: $e');
    }
  }

  // Smart cache validation
  bool _isCacheValid(DateTime? lastSync, Duration maxAge) {
    if (lastSync == null) return false;
    return DateTime.now().difference(lastSync) < maxAge;
  }

  // Profile operations with smart caching
  Future<List<models.Profile>> getProfiles() async {
    // Check if cache is still valid (profiles don't change often)
    const profilesCacheMaxAge = Duration(minutes: 30);

    if (_profilesCacheValid && _isCacheValid(_lastIncrementalSync, profilesCacheMaxAge)) {
      print('📱 Using valid profiles cache');
      _performanceMonitor.recordCacheHit('profiles');
      return _cachedProfiles;
    }

    _performanceMonitor.recordCacheMiss('profiles');
    if (_connectivityService.isOnline) {
      // Only sync if cache is invalid or stale
      print('📱 Profiles cache invalid, syncing...');
      _performanceMonitor.startOperation('sync_profiles');
      await _syncProfiles();
      _performanceMonitor.endOperation('sync_profiles');
      _profilesCacheValid = true;
      _lastIncrementalSync = DateTime.now();
    }

    return _cachedProfiles;
  }

  Future<models.Profile?> getProfileByPhone(String phoneNumber) async {
    // Clean phone number for matching
    final cleanedPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    try {
      return _cachedProfiles.firstWhere(
        (profile) => profile.phoneNumber.replaceAll(RegExp(r'[^\d]'), '') == cleanedPhone,
      );
    } catch (e) {
      return null;
    }
  }

  Future<models.Profile?> getProfileById(String profileId) async {
    try {
      // First try to find in cached profiles
      final cachedProfile = _cachedProfiles.firstWhere(
        (profile) => profile.id == profileId,
      );
      return cachedProfile;
    } catch (e) {
      // If not in cache, try local database
      final profile = await LocalDatabaseService.getProfileById(profileId);
      if (profile != null) {
        // Add to cache for future use
        _cachedProfiles.add(profile);
        return profile;
      }

      // If online, try to fetch from server
      if (_connectivityService.isOnline) {
        try {
          final serverProfile = await SupabaseService.getProfileById(profileId);
          if (serverProfile != null) {
            // Cache it locally
            await LocalDatabaseService.insertProfile(serverProfile);
            _cachedProfiles.add(serverProfile);
            return serverProfile;
          }
        } catch (e) {
          print('❌ Failed to fetch profile from server: $e');
        }
      }

      return null;
    }
  }

  Future<models.Profile?> getCurrentProfile() async {
    final currentUser = SupabaseService.currentUser;
    if (currentUser == null) return null;

    // Always check local database first for the most recent version
    try {
      final localProfile = await LocalDatabaseService.getProfileById(currentUser.id);
      if (localProfile != null) {
        print('📱 Found current profile in local database: ${localProfile.fullName}');

        // Update cache with the latest version from database
        final index = _cachedProfiles.indexWhere((p) => p.id == currentUser.id);
        if (index != -1) {
          _cachedProfiles[index] = localProfile;
        } else {
          _cachedProfiles.add(localProfile);
        }

        return localProfile;
      }
    } catch (e) {
      print('❌ Failed to get profile from local database: $e');
    }

    // If not in local database, try cache
    try {
      final cachedProfile = _cachedProfiles.firstWhere(
        (profile) => profile.id == currentUser.id,
      );
      print('📱 Found current profile in cache: ${cachedProfile.fullName}');
      return cachedProfile;
    } catch (e) {
      // Profile not in cache either
    }

    // If online, try to fetch from server
    if (_connectivityService.isOnline) {
      try {
        final serverProfile = await SupabaseService.getCurrentProfile();
        if (serverProfile != null) {
          print('📱 Fetched current profile from server: ${serverProfile.fullName}');
          // Cache it locally
          await LocalDatabaseService.insertProfile(serverProfile);
          _cachedProfiles.add(serverProfile);
          return serverProfile;
        }
      } catch (e) {
        print('❌ Failed to fetch current profile from server: $e');
      }
    }

    return null;
  }

  /// Update current user profile (offline-first)
  Future<models.Profile> updateCurrentProfile(models.Profile updatedProfile) async {
    try {
      print('📱 Updating current profile: ${updatedProfile.fullName}');

      // Always update local cache and database first
      await LocalDatabaseService.insertProfile(updatedProfile);

      // Force refresh from database to ensure we have the latest data
      final refreshedProfile = await refreshCurrentProfile();
      if (refreshedProfile != null) {
        // Use the refreshed profile to ensure consistency
        updatedProfile = refreshedProfile;
      }

      // If online, try to sync with server
      if (_connectivityService.isOnline) {
        try {
          await SupabaseService.updateProfile(updatedProfile);
          print('✅ Profile updated on server successfully');
        } catch (e) {
          print('❌ Failed to update profile on server: $e');
          // Queue for later sync
          await LocalDatabaseService.addToSyncQueue(
            operationType: 'UPDATE',
            tableName: 'profiles',
            recordId: updatedProfile.id,
            data: updatedProfile.toJson(),
          );
          print('📝 Profile update queued for sync');
        }
      } else {
        // Queue for later sync when online
        await LocalDatabaseService.addToSyncQueue(
          operationType: 'UPDATE',
          tableName: 'profiles',
          recordId: updatedProfile.id,
          data: updatedProfile.toJson(),
        );
        print('📝 Profile update queued for sync (offline)');
      }

      return updatedProfile;
    } catch (e) {
      print('❌ Failed to update current profile: $e');
      rethrow;
    }
  }

  // Category operations with smart caching
  Future<List<models.Category>> getUserCategories() async {
    final currentUser = SupabaseService.currentUser;
    if (currentUser == null) {
      print('❌ getUserCategories: No current user');
      return [];
    }

    print('📱 getUserCategories called for user: ${currentUser.id}');
    print('📱 Cache valid: $_categoriesCacheValid');
    print('📱 Cached categories count: ${_cachedCategories.length}');
    print('📱 Is initialized: $_isInitialized');

    // If not initialized, try to initialize first
    if (!_isInitialized) {
      print('📱 Service not initialized, initializing now...');
      await initialize();
    }

    // If cache is empty or invalid, try to load from local database first
    if (_cachedCategories.isEmpty) {
      print('📱 Cache is empty, loading from local database...');
      await _loadCachedData();

      if (_cachedCategories.isNotEmpty) {
        print('📱 Loaded ${_cachedCategories.length} categories from local database');
        final result = _cachedCategories.values.toList();
        return result;
      }
    }

    // Check if cache is still valid (categories change less frequently)
    const categoriesCacheMaxAge = Duration(minutes: 15);

    if (_categoriesCacheValid && _isCacheValid(_lastIncrementalSync, categoriesCacheMaxAge)) {
      print('📱 Using valid categories cache');
      final result = _cachedCategories.values.toList();
      print('📱 Returning ${result.length} cached categories');
      return result;
    }

    // Check if there are pending sync operations for categories
    final pendingOperations = await LocalDatabaseService.getPendingSyncOperations();
    final hasPendingCategoryChanges = pendingOperations.any(
      (op) => op['operation_type'] == 'UPDATE_CATEGORY'
    );

    // Check if we recently came online (within last 5 seconds)
    // This prevents immediate server sync after connectivity restoration
    final now = DateTime.now();
    final recentlyOnline = _lastConnectivityChange != null &&
        now.difference(_lastConnectivityChange!).inSeconds < 5;

    if (_connectivityService.isOnline &&
        !_isSyncing &&
        !hasPendingCategoryChanges &&
        !recentlyOnline) {
      // Only sync from server if there are no pending local changes and we're not recently online
      print('📱 No pending changes and stable connection - safe to sync from server');
      try {
        await _syncCategories(currentUser.id);
        _categoriesCacheValid = true;
        _lastIncrementalSync = DateTime.now();
      } catch (e) {
        print('❌ Failed to sync categories: $e');
        // Fall back to local data
        await _loadCachedData();
      }
    } else if (hasPendingCategoryChanges) {
      print('📱 Pending category changes detected - using local data only');
    } else if (recentlyOnline) {
      print('📱 Recently came online - using local data to avoid conflicts');
    }

    // Return unique categories (remove any potential duplicates)
    final uniqueCategories = <String, models.Category>{};
    for (final category in _cachedCategories.values) {
      uniqueCategories[category.id] = category;
    }

    final result = uniqueCategories.values.toList();
    print('📱 getUserCategories returning ${result.length} unique categories');
    for (final category in result) {
      print('📱   - ${category.type.name} (ID: ${category.id})');
    }

    return result;
  }

  /// Refresh categories cache from local database
  /// This should be called after sync operations complete
  Future<void> refreshCategoriesCache() async {
    try {
      final currentUser = SupabaseService.currentUser;
      if (currentUser == null) return;

      print('📱 Refreshing categories cache from local database...');

      // Invalidate cache to force fresh load
      _categoriesCacheValid = false;

      // Load fresh categories from local database
      final categories = await LocalDatabaseService.getCategoriesByUserId(currentUser.id);

      // Update cache
      _cachedCategories.clear();
      for (final category in categories) {
        _cachedCategories[category.id] = category;
      }

      // Mark cache as valid again with fresh data
      _categoriesCacheValid = true;
      _lastIncrementalSync = DateTime.now();

      print('📱 Refreshed cache with ${categories.length} categories');
      notifyListeners();
    } catch (e) {
      print('❌ Failed to refresh categories cache: $e');
    }
  }

  Future<models.Category?> getCategoryById(String categoryId) async {
    // Check cache first
    if (_cachedCategories.containsKey(categoryId)) {
      return _cachedCategories[categoryId];
    }

    // Try local database
    final category = await LocalDatabaseService.getCategoryById(categoryId);
    if (category != null) {
      _cachedCategories[categoryId] = category;
      return category;
    }

    // If online, try to fetch from server
    if (_connectivityService.isOnline) {
      try {
        final currentUser = SupabaseService.currentUser;
        if (currentUser != null) {
          await _syncCategories(currentUser.id);
          return _cachedCategories[categoryId];
        }
      } catch (e) {
        print('❌ Failed to fetch category from server: $e');
      }
    }

    return null;
  }

  // Assignment operations
  Future<models.Category?> getCategoryAssignedToContact({
    required String currentUserId,
    required String contactPhone,
  }) async {
    try {
      // CACHE-FIRST APPROACH: Check memory cache first for instant response
      if (_contactAssignmentsCache.containsKey(contactPhone)) {
        final cachedCategory = _contactAssignmentsCache[contactPhone];
        print('📱 Cache hit: Found assigned category ${cachedCategory?.type.name ?? 'null'} for $contactPhone');
        return cachedCategory;
      }

      // Try different phone number variations in cache
      final phoneVariations = ContactsService.generatePhoneVariations(contactPhone);
      for (final variation in phoneVariations) {
        if (_contactAssignmentsCache.containsKey(variation)) {
          final cachedCategory = _contactAssignmentsCache[variation];
          print('📱 Cache hit (variation): Found assigned category ${cachedCategory?.type.name ?? 'null'} for $variation');
          // Cache the result for the original phone number too
          _contactAssignmentsCache[contactPhone] = cachedCategory;
          return cachedCategory;
        }
      }

      // Not in cache, try offline database first (faster than network)
      final offlineCategory = await _syncService.getCategoryAssignedToContactOffline(
        currentUserId: currentUserId,
        contactPhone: contactPhone,
      );

      if (offlineCategory != null) {
        print('📱 Offline: Found assigned category ${offlineCategory.type.name} with ${offlineCategory.timeSlots.length} time slots');
        // Cache for future use
        _contactAssignmentsCache[contactPhone] = offlineCategory;
        _cachedCategories[offlineCategory.id] = offlineCategory;
        return offlineCategory;
      }

      // Only fetch from server if not found locally and we're online
      if (_connectivityService.isOnline) {
        final onlineCategory = await SupabaseService.getCategoryAssignedToContact(
          currentUserId: currentUserId,
          contactPhone: contactPhone,
        );

        if (onlineCategory != null) {
          print('📱 Online: Found assigned category ${onlineCategory.type.name} with ${onlineCategory.timeSlots.length} time slots');

          // Cache in memory for instant future access
          _contactAssignmentsCache[contactPhone] = onlineCategory;
          _cachedCategories[onlineCategory.id] = onlineCategory;

          // Store in local database for offline access
          print('📱 Storing assigned category ${onlineCategory.id} with time slots in local database for offline access');
          await LocalDatabaseService.insertCategory(onlineCategory);

          return onlineCategory;
        }
      }

      // No assignment found - cache this result too to avoid repeated lookups
      _contactAssignmentsCache[contactPhone] = null;
      print('📱 No category assigned to contact $contactPhone');
      return null;

    } catch (e) {
      print('❌ Failed to get assigned category: $e');
      // Fall back to offline data
      return await _syncService.getCategoryAssignedToContactOffline(
        currentUserId: currentUserId,
        contactPhone: contactPhone,
      );
    }
  }

  // Get how a contact has categorized the current user
  Future<models.Category?> getContactCategory({
    required String contactUserId,
    required String callerPhone,
  }) async {
    try {
      if (_connectivityService.isOnline) {
        // Try online first
        final category = await SupabaseService.getContactCategory(
          contactUserId: contactUserId,
          callerPhone: callerPhone,
        );

        if (category != null) {
          print('📱 Online: Found category ${category.type.name} with ${category.timeSlots.length} time slots');

          // Cache the category in memory
          _cachedCategories[category.id] = category;

          // IMPORTANT: Also store in local database for offline access
          print('📱 Storing category ${category.id} with time slots in local database for offline access');
          await LocalDatabaseService.insertCategory(category);

          return category;
        }
      }

      // Fall back to offline data - this is the reverse lookup
      // We need to find how contactUserId categorized callerPhone
      return await _syncService.getContactCategoryOffline(
        contactUserId: contactUserId,
        callerPhone: callerPhone,
      );
    } catch (e) {
      print('❌ Failed to get contact category: $e');
      // Fall back to offline data
      return await _syncService.getContactCategoryOffline(
        contactUserId: contactUserId,
        callerPhone: callerPhone,
      );
    }
  }

  // Enhanced version that accepts contact's phone numbers for better matching
  Future<models.Category?> getContactCategoryWithPhones({
    required String contactUserId,
    required String callerPhone,
    required List<String> contactPhoneNumbers,
  }) async {
    try {
      print('🔍 getContactCategoryWithPhones called:');
      print('🔍   contactUserId: $contactUserId (who did the categorizing)');
      print('🔍   callerPhone: $callerPhone (current user - who was categorized)');
      print('🔍   contactPhoneNumbers: $contactPhoneNumbers (contact\'s phone numbers)');

      if (_connectivityService.isOnline) {
        // Try online first
        final category = await SupabaseService.getContactCategory(
          contactUserId: contactUserId,
          callerPhone: callerPhone,
        );

        if (category != null) {
          print('📱 Online: Found category ${category.type.name} with ${category.timeSlots.length} time slots');

          // Cache the category in memory
          _cachedCategories[category.id] = category;

          // IMPORTANT: Also store in local database for offline access
          print('📱 Storing category ${category.id} with time slots in local database for offline access');
          await LocalDatabaseService.insertCategory(category);

          return category;
        }
      }

      // Fall back to offline data with enhanced phone matching
      return await _syncService.getContactCategoryOffline(
        contactUserId: contactUserId,
        callerPhone: callerPhone,
        contactPhoneNumbers: contactPhoneNumbers,
      );
    } catch (e) {
      print('❌ Failed to get contact category with phones: $e');
      // Fall back to offline data with enhanced phone matching
      return await _syncService.getContactCategoryOffline(
        contactUserId: contactUserId,
        callerPhone: callerPhone,
        contactPhoneNumbers: contactPhoneNumbers,
      );
    }
  }

  Future<void> assignContactToCategory({
    required String userId,
    required String contactPhone,
    required String categoryId,
  }) async {
    try {
      // Get the category for immediate UI update
      final category = _cachedCategories[categoryId];

      // Update local cache immediately for instant UI feedback
      if (category != null) {
        _contactAssignmentsCache[contactPhone] = category;

        // Also cache phone number variations for faster lookup
        final phoneVariations = ContactsService.generatePhoneVariations(contactPhone);
        for (final variation in phoneVariations) {
          _contactAssignmentsCache[variation] = category;
        }

        // Emit immediate update to UI
        _contactAssignmentsStreamController.add(Map.from(_contactAssignmentsCache));
        print('📱 Updated UI immediately with new assignment (including variations)');
      }

      if (_connectivityService.isOnline) {
        // Try online assignment first
        await SupabaseService.assignContactToCategory(
          userId: userId,
          contactPhone: contactPhone,
          categoryId: categoryId,
        );
        print('✅ Contact assigned online');
      } else {
        // Store offline and queue for sync
        await _syncService.assignContactToCategoryOffline(
          userId: userId,
          contactPhone: contactPhone,
          categoryId: categoryId,
        );
        print('✅ Contact assigned offline (queued for sync)');
      }
    } catch (e) {
      print('❌ Online assignment failed, falling back to offline: $e');
      // Fall back to offline assignment
      await _syncService.assignContactToCategoryOffline(
        userId: userId,
        contactPhone: contactPhone,
        categoryId: categoryId,
      );
      print('✅ Contact assigned offline (queued for sync)');
    }
  }

  // Get assignment from cache for instant access
  models.Category? getCachedContactAssignment(String contactPhone) {
    return _contactAssignmentsCache[contactPhone];
  }

  // Update a single contact assignment in real-time
  void updateContactAssignment(String contactPhone, models.Category? category) {
    // Update cache with multiple key variations for better matching
    final cleanedPhone = contactPhone.replaceAll(RegExp(r'[^\d]'), '');
    final cacheKeys = [
      contactPhone,
      cleanedPhone,
      'contact_category_$cleanedPhone',
      'contact_category_$contactPhone',
    ];

    for (final key in cacheKeys) {
      if (category != null) {
        _contactAssignmentsCache[key] = category;
      } else {
        _contactAssignmentsCache.remove(key);
      }
    }

    // Emit stream update immediately for UI responsiveness
    if (!_contactAssignmentsStreamController.isClosed) {
      _contactAssignmentsStreamController.add(Map.from(_contactAssignmentsCache));
    }
    print('📱 Real-time assignment update: $contactPhone -> ${category?.type.name ?? 'null'}');
  }

  // Add a new category and emit to stream
  void addCategory(models.Category category) {
    _cachedCategories[category.id] = category;
    _categoriesStreamController.add(_cachedCategories.values.toList());
    print('📱 Added new category: ${category.type.name}');
  }

  // Update an existing category and emit to stream
  void updateCategory(models.Category category) {
    _cachedCategories[category.id] = category;
    _categoriesStreamController.add(_cachedCategories.values.toList());
    print('📱 Updated category: ${category.type.name}');
  }

  // Add new profiles and emit to stream
  void addProfiles(List<models.Profile> profiles) {
    for (final profile in profiles) {
      // Avoid duplicates
      final existingIndex = _cachedProfiles.indexWhere((p) => p.id == profile.id);
      if (existingIndex >= 0) {
        _cachedProfiles[existingIndex] = profile;
      } else {
        _cachedProfiles.add(profile);
      }
    }
    _profilesStreamController.add(List.from(_cachedProfiles));
    print('📱 Added/updated ${profiles.length} profiles');
  }

  // Sync notification preferences
  Future<void> _syncNotificationPreferences(String userId) async {
    try {
      print('📱 Syncing notification preferences...');

      // Get all notification preferences for the current user
      final serverPreferences = await SupabaseService.getAllUserNotificationPreferences(userId);

      // Store in local database
      for (final preference in serverPreferences) {
        await LocalDatabaseService.insertNotificationPreference(preference);
      }

      print('📱 Synced ${serverPreferences.length} notification preferences');
    } catch (e) {
      print('❌ Failed to sync notification preferences: $e');
    }
  }

  // Get notification preferences (offline-aware)
  Future<List<models.NotificationPreference>> getNotificationPreferences({
    required String userId,
    required String contactUserId,
  }) async {
    if (_connectivityService.isOnline) {
      try {
        // Try to get from server first
        final serverPreferences = await SupabaseService.getNotificationPreferences(
          userId: userId,
          contactUserId: contactUserId,
        );

        // Cache them locally
        for (final preference in serverPreferences) {
          await LocalDatabaseService.insertNotificationPreference(preference);
        }

        return serverPreferences;
      } catch (e) {
        print('❌ Failed to get notification preferences from server: $e');
        // Fall back to local data
      }
    }

    // Get from local database
    return await LocalDatabaseService.getNotificationPreferences(
      userId: userId,
      contactUserId: contactUserId,
    );
  }

  // Update notification preference (offline-aware)
  Future<void> updateNotificationPreference({
    required String userId,
    required String contactUserId,
    required String timeSlotId,
    required bool isEnabled,
  }) async {
    // Always update locally first
    await LocalDatabaseService.updateNotificationPreference(
      userId: userId,
      contactUserId: contactUserId,
      timeSlotId: timeSlotId,
      isEnabled: isEnabled,
    );

    // Try to sync to server if online
    if (_connectivityService.isOnline) {
      try {
        await SupabaseService.toggleNotificationPreference(
          userId: userId,
          contactUserId: contactUserId,
          timeSlotId: timeSlotId,
          isEnabled: isEnabled,
        );
        print('✅ Notification preference synced to server');
      } catch (e) {
        print('❌ Failed to sync notification preference to server: $e');
        // Add to sync queue for later
        await LocalDatabaseService.addToSyncQueue(
          operationType: 'UPDATE_NOTIFICATION_PREFERENCE',
          tableName: 'notification_preferences',
          recordId: '${userId}_${contactUserId}_$timeSlotId',
          data: {
            'user_id': userId,
            'contact_user_id': contactUserId,
            'time_slot_id': timeSlotId,
            'is_enabled': isEnabled,
          },
        );
      }
    } else {
      // Add to sync queue for when online
      await LocalDatabaseService.addToSyncQueue(
        operationType: 'UPDATE_NOTIFICATION_PREFERENCE',
        tableName: 'notification_preferences',
        recordId: '${userId}_${contactUserId}_$timeSlotId',
        data: {
          'user_id': userId,
          'contact_user_id': contactUserId,
          'time_slot_id': timeSlotId,
          'is_enabled': isEnabled,
        },
      );
    }
  }

  // Notification operations
  Future<void> scheduleNotification({
    required String contactPhone,
    required String contactName,
    required String timeSlotId,
    required DateTime scheduledTime,
  }) async {
    try {
      // Always store locally for notifications
      await _syncService.scheduleNotificationOffline(
        contactPhone: contactPhone,
        contactName: contactName,
        timeSlotId: timeSlotId,
        scheduledTime: scheduledTime,
      );
      print('✅ Notification scheduled');
    } catch (e) {
      print('❌ Failed to schedule notification: $e');
      rethrow;
    }
  }

  // Smart cache management
  void invalidateCache({
    bool profiles = false,
    bool categories = false,
    bool assignments = false,
  }) {
    if (profiles) {
      _profilesCacheValid = false;
      print('📱 Profiles cache invalidated');
    }
    if (categories) {
      _categoriesCacheValid = false;
      print('📱 Categories cache invalidated');
    }
    if (assignments) {
      _assignmentsCacheValid = false;
      print('📱 Assignments cache invalidated');
    }
  }

  void invalidateAllCaches() {
    _profilesCacheValid = false;
    _categoriesCacheValid = false;
    _assignmentsCacheValid = false;
    _lastFullSync = null;
    _lastIncrementalSync = null;
    print('📱 All caches invalidated');
  }

  Future<void> refreshCache() async {
    invalidateAllCaches();
    await _loadCachedData();
  }

  /// Load cached data without invalidating existing cache (cache-first approach)
  Future<void> loadCachedDataOnly() async {
    try {
      print('📱 Loading cached data only (preserving existing cache)...');
      await _loadCachedData();
    } catch (e) {
      print('❌ Failed to load cached data: $e');
    }
  }

  /// Invalidate profile cache to force refresh from database
  Future<void> invalidateProfileCache() async {
    final currentUser = SupabaseService.currentUser;
    if (currentUser != null) {
      // Remove current user's profile from cache to force refresh
      _cachedProfiles.removeWhere((profile) => profile.id == currentUser.id);
      print('📱 Invalidated profile cache for user: ${currentUser.id}');
    }
  }

  /// Force refresh current profile from database and update cache
  Future<models.Profile?> refreshCurrentProfile() async {
    final currentUser = SupabaseService.currentUser;
    if (currentUser == null) return null;

    try {
      // Clear cache first
      _cachedProfiles.removeWhere((profile) => profile.id == currentUser.id);

      // Get fresh data from database
      final freshProfile = await LocalDatabaseService.getProfileById(currentUser.id);
      if (freshProfile != null) {
        // Update cache with fresh data
        _cachedProfiles.add(freshProfile);
        print('📱 Refreshed profile from database: ${freshProfile.fullName} (${freshProfile.username})');

        // Notify listeners about the change
        notifyListeners();

        return freshProfile;
      }
    } catch (e) {
      print('❌ Failed to refresh profile from database: $e');
    }

    return null;
  }

  Future<void> clearCache() async {
    await LocalDatabaseService.clearAllData();
    _cachedProfiles.clear();
    _cachedCategories.clear();
    _contactAssignmentsCache.clear();
    invalidateAllCaches();
    notifyListeners();
  }

  // Optimized sync that only fetches what's needed
  Future<void> smartSync() async {
    if (!_connectivityService.isOnline) {
      print('📱 Cannot sync - device is offline');
      return;
    }

    try {
      final currentUser = SupabaseService.currentUser;
      if (currentUser == null) {
        print('📱 No current user - skipping sync');
        return;
      }

      print('📱 Starting smart sync...');

      // Only sync what's needed based on cache validity
      const maxAge = Duration(minutes: 10);
      final now = DateTime.now();

      if (!_profilesCacheValid || !_isCacheValid(_lastIncrementalSync, maxAge)) {
        print('📱 Syncing profiles (cache invalid)');
        await _syncProfiles();
        _profilesCacheValid = true;
      }

      if (!_categoriesCacheValid || !_isCacheValid(_lastIncrementalSync, maxAge)) {
        print('📱 Syncing categories (cache invalid)');
        await _syncCategories(currentUser.id);
        _categoriesCacheValid = true;
      }

      if (!_assignmentsCacheValid || !_isCacheValid(_lastIncrementalSync, maxAge)) {
        print('📱 Syncing user contacts (cache invalid)');
        await _syncUserContacts(currentUser.id);
        _assignmentsCacheValid = true;
      }

      _lastIncrementalSync = now;

      // Trigger sync service for pending operations
      await _syncService.syncPendingOperations();

      // After syncing, reload cached data to ensure consistency
      await _loadCachedData();

      print('✅ Smart sync completed');
    } catch (e) {
      print('❌ Smart sync failed: $e');
    }
  }

  bool get isOnline => _connectivityService.isOnline;
  bool get isSyncing => _syncService.isSyncing;

  // Dispose method to clean up stream controllers
  void dispose() {
    _profilesStreamController.close();
    _categoriesStreamController.close();
    _contactAssignmentsStreamController.close();
    _syncStatusStreamController.close();
    super.dispose();
  }

  // Emit sync status updates
  void _emitSyncStatus(bool isSyncing) {
    _syncStatusStreamController.add(isSyncing);
  }

  // Enhanced sync methods that emit stream updates
  Future<void> syncWithServerAndEmit() async {
    _emitSyncStatus(true);
    try {
      await syncWithServer();
      // Reload and emit fresh data after sync
      await _loadCachedData();
    } finally {
      _emitSyncStatus(false);
    }
  }

  Future<void> incrementalSyncAndEmit(DateTime? lastSyncTime) async {
    _emitSyncStatus(true);
    try {
      await incrementalSync(lastSyncTime);
      // Reload and emit fresh data after sync
      await _loadCachedData();
    } finally {
      _emitSyncStatus(false);
    }
  }
}
