import 'category.dart';

class UserContact {
  final String id;
  final String userId;
  final String categorizedContactPhone;
  final String assignedCategoryId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Category? category; // Populated when joined with categories table
  final String? contactName; // From device contacts

  UserContact({
    required this.id,
    required this.userId,
    required this.categorizedContactPhone,
    required this.assignedCategoryId,
    required this.createdAt,
    required this.updatedAt,
    this.category,
    this.contactName,
  });

  factory UserContact.fromJson(Map<String, dynamic> json) {
    return UserContact(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      categorizedContactPhone: json['categorized_contact_phone'] as String,
      assignedCategoryId: json['assigned_category_id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      category: json['category'] != null
          ? Category.fromJson(json['category'] as Map<String, dynamic>)
          : null,
      contactName: json['contact_name'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'categorized_contact_phone': categorizedContactPhone,
      'assigned_category_id': assignedCategoryId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  UserContact copyWith({
    String? id,
    String? userId,
    String? categorizedContactPhone,
    String? assignedCategoryId,
    DateTime? createdAt,
    DateTime? updatedAt,
    Category? category,
    String? contactName,
  }) {
    return UserContact(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      categorizedContactPhone: categorizedContactPhone ?? this.categorizedContactPhone,
      assignedCategoryId: assignedCategoryId ?? this.assignedCategoryId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      category: category ?? this.category,
      contactName: contactName ?? this.contactName,
    );
  }

  String get displayName {
    return contactName ?? categorizedContactPhone;
  }

  String get formattedPhone {
    // Basic phone number formatting
    final cleaned = categorizedContactPhone.replaceAll(RegExp(r'[^\d]'), '');
    if (cleaned.length == 10) {
      return '(${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6)}';
    } else if (cleaned.length == 11 && cleaned.startsWith('1')) {
      return '+1 (${cleaned.substring(1, 4)}) ${cleaned.substring(4, 7)}-${cleaned.substring(7)}';
    }
    return categorizedContactPhone;
  }

  @override
  String toString() {
    return 'UserContact(id: $id, contactName: $contactName, phone: $categorizedContactPhone, categoryId: $assignedCategoryId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserContact && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class DeviceContact {
  final String? id;
  final String displayName;
  final List<String> phoneNumbers;
  final String? email;
  final String? avatar;

  DeviceContact({
    this.id,
    required this.displayName,
    required this.phoneNumbers,
    this.email,
    this.avatar,
  });

  String get primaryPhone {
    return phoneNumbers.isNotEmpty ? phoneNumbers.first : '';
  }

  String get cleanedPrimaryPhone {
    return primaryPhone.replaceAll(RegExp(r'[^\d]'), '');
  }

  @override
  String toString() {
    return 'DeviceContact(name: $displayName, phones: $phoneNumbers)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeviceContact && 
           other.displayName == displayName && 
           other.primaryPhone == primaryPhone;
  }

  @override
  int get hashCode => displayName.hashCode ^ primaryPhone.hashCode;
}
