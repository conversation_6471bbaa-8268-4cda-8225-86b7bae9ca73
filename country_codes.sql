-- International Country Codes for Phone Number Normalization
-- This file contains all international country codes used for phone number normalization
-- in the Contact Times app

-- Create a function to normalize phone numbers by removing country codes and + prefix
CREATE OR REPLACE FUNCTION normalize_phone_number(phone_number TEXT)
RETURNS TEXT AS $$
BEGIN
    -- Return empty string if input is null or empty
    IF phone_number IS NULL OR phone_number = '' THEN
        RETURN '';
    END IF;

    -- Remove all non-digit characters first
    phone_number := REGEXP_REPLACE(phone_number, '[^\d]', '', 'g');

    -- Handle country codes (ordered by length to avoid partial matches)
    -- 4-digit country codes
    IF phone_number LIKE '1684%' THEN RETURN SUBSTRING(phone_number FROM 5); -- American Samoa
    ELSIF phone_number LIKE '1264%' THEN RETURN SUBSTRING(phone_number FROM 5); -- <PERSON><PERSON><PERSON>
    ELSIF phone_number LIKE '1268%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Antigua and Barbuda
    ELSIF phone_number LIKE '1242%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Bahamas
    ELSIF phone_number LIKE '1246%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Barbados
    ELSIF phone_number LIKE '1441%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Bermuda
    ELSIF phone_number LIKE '1284%' THEN RETURN SUBSTRING(phone_number FROM 5); -- British Virgin Islands
    ELSIF phone_number LIKE '1345%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Cayman Islands
    ELSIF phone_number LIKE '1767%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Dominica
    ELSIF phone_number LIKE '1809%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Dominican Republic
    ELSIF phone_number LIKE '1829%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Dominican Republic
    ELSIF phone_number LIKE '1849%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Dominican Republic
    ELSIF phone_number LIKE '1473%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Grenada
    ELSIF phone_number LIKE '1671%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Guam
    ELSIF phone_number LIKE '1876%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Jamaica
    ELSIF phone_number LIKE '1664%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Montserrat
    ELSIF phone_number LIKE '1670%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Northern Mariana Islands
    ELSIF phone_number LIKE '1787%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Puerto Rico
    ELSIF phone_number LIKE '1939%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Puerto Rico
    ELSIF phone_number LIKE '1869%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Saint Kitts and Nevis
    ELSIF phone_number LIKE '1758%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Saint Lucia
    ELSIF phone_number LIKE '1784%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Saint Vincent and the Grenadines
    ELSIF phone_number LIKE '1868%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Trinidad and Tobago
    ELSIF phone_number LIKE '1649%' THEN RETURN SUBSTRING(phone_number FROM 5); -- Turks and Caicos Islands

    -- 3-digit country codes
    ELSIF phone_number LIKE '355%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Albania
    ELSIF phone_number LIKE '213%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Algeria
    ELSIF phone_number LIKE '376%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Andorra
    ELSIF phone_number LIKE '244%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Angola
    ELSIF phone_number LIKE '374%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Armenia
    ELSIF phone_number LIKE '297%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Aruba
    ELSIF phone_number LIKE '994%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Azerbaijan
    ELSIF phone_number LIKE '973%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Bahrain
    ELSIF phone_number LIKE '880%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Bangladesh
    ELSIF phone_number LIKE '375%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Belarus
    ELSIF phone_number LIKE '501%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Belize
    ELSIF phone_number LIKE '229%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Benin
    ELSIF phone_number LIKE '975%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Bhutan
    ELSIF phone_number LIKE '591%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Bolivia
    ELSIF phone_number LIKE '387%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Bosnia and Herzegovina
    ELSIF phone_number LIKE '267%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Botswana
    ELSIF phone_number LIKE '673%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Brunei Darussalam
    ELSIF phone_number LIKE '359%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Bulgaria
    ELSIF phone_number LIKE '226%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Burkina Faso
    ELSIF phone_number LIKE '257%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Burundi
    ELSIF phone_number LIKE '855%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Cambodia
    ELSIF phone_number LIKE '237%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Cameroon
    ELSIF phone_number LIKE '238%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Cape Verde
    ELSIF phone_number LIKE '236%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Central African Republic
    ELSIF phone_number LIKE '235%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Chad
    ELSIF phone_number LIKE '269%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Comoros
    ELSIF phone_number LIKE '243%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Congo (Dem. Rep.)
    ELSIF phone_number LIKE '242%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Congo (Rep.)
    ELSIF phone_number LIKE '682%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Cook Islands
    ELSIF phone_number LIKE '506%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Costa Rica
    ELSIF phone_number LIKE '225%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Côte d'Ivoire
    ELSIF phone_number LIKE '385%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Croatia
    ELSIF phone_number LIKE '599%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Curaçao
    ELSIF phone_number LIKE '357%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Cyprus
    ELSIF phone_number LIKE '420%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Czech Republic
    ELSIF phone_number LIKE '253%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Djibouti
    ELSIF phone_number LIKE '593%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Ecuador
    ELSIF phone_number LIKE '503%' THEN RETURN SUBSTRING(phone_number FROM 4); -- El Salvador
    ELSIF phone_number LIKE '240%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Equatorial Guinea
    ELSIF phone_number LIKE '291%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Eritrea
    ELSIF phone_number LIKE '372%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Estonia
    ELSIF phone_number LIKE '268%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Eswatini (Swaziland)
    ELSIF phone_number LIKE '251%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Ethiopia
    ELSIF phone_number LIKE '500%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Falkland Islands
    ELSIF phone_number LIKE '298%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Faroe Islands
    ELSIF phone_number LIKE '679%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Fiji
    ELSIF phone_number LIKE '358%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Finland
    ELSIF phone_number LIKE '594%' THEN RETURN SUBSTRING(phone_number FROM 4); -- French Guiana
    ELSIF phone_number LIKE '689%' THEN RETURN SUBSTRING(phone_number FROM 4); -- French Polynesia
    ELSIF phone_number LIKE '241%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Gabon
    ELSIF phone_number LIKE '220%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Gambia
    ELSIF phone_number LIKE '995%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Georgia
    ELSIF phone_number LIKE '233%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Ghana
    ELSIF phone_number LIKE '350%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Gibraltar
    ELSIF phone_number LIKE '299%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Greenland
    ELSIF phone_number LIKE '590%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Guadeloupe
    ELSIF phone_number LIKE '502%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Guatemala
    ELSIF phone_number LIKE '224%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Guinea
    ELSIF phone_number LIKE '245%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Guinea-Bissau
    ELSIF phone_number LIKE '592%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Guyana
    ELSIF phone_number LIKE '509%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Haiti
    ELSIF phone_number LIKE '504%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Honduras
    ELSIF phone_number LIKE '852%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Hong Kong
    ELSIF phone_number LIKE '354%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Iceland
    ELSIF phone_number LIKE '964%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Iraq
    ELSIF phone_number LIKE '353%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Ireland
    ELSIF phone_number LIKE '972%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Israel
    ELSIF phone_number LIKE '962%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Jordan
    ELSIF phone_number LIKE '686%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Kiribati
    ELSIF phone_number LIKE '383%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Kosovo
    ELSIF phone_number LIKE '965%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Kuwait
    ELSIF phone_number LIKE '996%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Kyrgyzstan
    ELSIF phone_number LIKE '856%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Laos
    ELSIF phone_number LIKE '371%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Latvia
    ELSIF phone_number LIKE '961%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Lebanon
    ELSIF phone_number LIKE '266%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Lesotho
    ELSIF phone_number LIKE '231%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Liberia
    ELSIF phone_number LIKE '218%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Libya
    ELSIF phone_number LIKE '423%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Liechtenstein
    ELSIF phone_number LIKE '370%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Lithuania
    ELSIF phone_number LIKE '352%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Luxembourg
    ELSIF phone_number LIKE '853%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Macao
    ELSIF phone_number LIKE '261%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Madagascar
    ELSIF phone_number LIKE '265%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Malawi
    ELSIF phone_number LIKE '960%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Maldives
    ELSIF phone_number LIKE '223%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Mali
    ELSIF phone_number LIKE '356%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Malta
    ELSIF phone_number LIKE '692%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Marshall Islands
    ELSIF phone_number LIKE '596%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Martinique
    ELSIF phone_number LIKE '222%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Mauritania
    ELSIF phone_number LIKE '230%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Mauritius
    ELSIF phone_number LIKE '691%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Micronesia
    ELSIF phone_number LIKE '373%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Moldova
    ELSIF phone_number LIKE '377%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Monaco
    ELSIF phone_number LIKE '976%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Mongolia
    ELSIF phone_number LIKE '382%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Montenegro
    ELSIF phone_number LIKE '212%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Morocco
    ELSIF phone_number LIKE '258%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Mozambique
    ELSIF phone_number LIKE '264%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Namibia
    ELSIF phone_number LIKE '674%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Nauru
    ELSIF phone_number LIKE '977%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Nepal
    ELSIF phone_number LIKE '687%' THEN RETURN SUBSTRING(phone_number FROM 4); -- New Caledonia
    ELSIF phone_number LIKE '505%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Nicaragua
    ELSIF phone_number LIKE '227%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Niger
    ELSIF phone_number LIKE '234%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Nigeria
    ELSIF phone_number LIKE '683%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Niue
    ELSIF phone_number LIKE '850%' THEN RETURN SUBSTRING(phone_number FROM 4); -- North Korea
    ELSIF phone_number LIKE '389%' THEN RETURN SUBSTRING(phone_number FROM 4); -- North Macedonia
    ELSIF phone_number LIKE '968%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Oman
    ELSIF phone_number LIKE '680%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Palau
    ELSIF phone_number LIKE '970%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Palestine
    ELSIF phone_number LIKE '507%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Panama
    ELSIF phone_number LIKE '675%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Papua New Guinea
    ELSIF phone_number LIKE '595%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Paraguay
    ELSIF phone_number LIKE '974%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Qatar
    ELSIF phone_number LIKE '262%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Reunion
    ELSIF phone_number LIKE '250%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Rwanda
    ELSIF phone_number LIKE '290%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Saint Helena
    ELSIF phone_number LIKE '508%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Saint Pierre and Miquelon
    ELSIF phone_number LIKE '685%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Samoa
    ELSIF phone_number LIKE '378%' THEN RETURN SUBSTRING(phone_number FROM 4); -- San Marino
    ELSIF phone_number LIKE '239%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Sao Tome and Principe
    ELSIF phone_number LIKE '966%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Saudi Arabia
    ELSIF phone_number LIKE '221%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Senegal
    ELSIF phone_number LIKE '381%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Serbia
    ELSIF phone_number LIKE '248%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Seychelles
    ELSIF phone_number LIKE '232%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Sierra Leone
    ELSIF phone_number LIKE '421%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Slovakia
    ELSIF phone_number LIKE '386%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Slovenia
    ELSIF phone_number LIKE '677%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Solomon Islands
    ELSIF phone_number LIKE '252%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Somalia
    ELSIF phone_number LIKE '211%' THEN RETURN SUBSTRING(phone_number FROM 4); -- South Sudan
    ELSIF phone_number LIKE '249%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Sudan
    ELSIF phone_number LIKE '597%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Suriname
    ELSIF phone_number LIKE '963%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Syria
    ELSIF phone_number LIKE '886%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Taiwan
    ELSIF phone_number LIKE '992%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Tajikistan
    ELSIF phone_number LIKE '255%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Tanzania
    ELSIF phone_number LIKE '670%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Timor-Leste
    ELSIF phone_number LIKE '228%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Togo
    ELSIF phone_number LIKE '690%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Tokelau
    ELSIF phone_number LIKE '676%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Tonga
    ELSIF phone_number LIKE '216%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Tunisia
    ELSIF phone_number LIKE '993%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Turkmenistan
    ELSIF phone_number LIKE '688%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Tuvalu
    ELSIF phone_number LIKE '256%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Uganda
    ELSIF phone_number LIKE '380%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Ukraine
    ELSIF phone_number LIKE '971%' THEN RETURN SUBSTRING(phone_number FROM 4); -- United Arab Emirates
    ELSIF phone_number LIKE '598%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Uruguay
    ELSIF phone_number LIKE '998%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Uzbekistan
    ELSIF phone_number LIKE '678%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Vanuatu
    ELSIF phone_number LIKE '379%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Vatican City State
    ELSIF phone_number LIKE '681%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Wallis and Futuna
    ELSIF phone_number LIKE '967%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Yemen
    ELSIF phone_number LIKE '260%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Zambia
    ELSIF phone_number LIKE '263%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Zimbabwe

    -- 2-digit country codes
    ELSIF phone_number LIKE '93%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Afghanistan
    ELSIF phone_number LIKE '54%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Argentina
    ELSIF phone_number LIKE '61%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Australia
    ELSIF phone_number LIKE '43%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Austria
    ELSIF phone_number LIKE '32%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Belgium
    ELSIF phone_number LIKE '55%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Brazil
    ELSIF phone_number LIKE '56%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Chile
    ELSIF phone_number LIKE '86%' THEN RETURN SUBSTRING(phone_number FROM 3); -- China
    ELSIF phone_number LIKE '57%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Colombia
    ELSIF phone_number LIKE '53%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Cuba
    ELSIF phone_number LIKE '45%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Denmark
    ELSIF phone_number LIKE '20%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Egypt
    ELSIF phone_number LIKE '33%' THEN RETURN SUBSTRING(phone_number FROM 3); -- France
    ELSIF phone_number LIKE '49%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Germany
    ELSIF phone_number LIKE '30%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Greece
    ELSIF phone_number LIKE '36%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Hungary
    ELSIF phone_number LIKE '91%' THEN RETURN SUBSTRING(phone_number FROM 3); -- India
    ELSIF phone_number LIKE '62%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Indonesia
    ELSIF phone_number LIKE '98%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Iran
    ELSIF phone_number LIKE '39%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Italy
    ELSIF phone_number LIKE '81%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Japan
    ELSIF phone_number LIKE '60%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Malaysia
    ELSIF phone_number LIKE '52%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Mexico
    ELSIF phone_number LIKE '31%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Netherlands
    ELSIF phone_number LIKE '64%' THEN RETURN SUBSTRING(phone_number FROM 3); -- New Zealand
    ELSIF phone_number LIKE '47%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Norway
    ELSIF phone_number LIKE '92%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Pakistan
    ELSIF phone_number LIKE '51%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Peru
    ELSIF phone_number LIKE '63%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Philippines
    ELSIF phone_number LIKE '48%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Poland
    ELSIF phone_number LIKE '40%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Romania
    ELSIF phone_number LIKE '65%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Singapore
    ELSIF phone_number LIKE '27%' THEN RETURN SUBSTRING(phone_number FROM 3); -- South Africa
    ELSIF phone_number LIKE '82%' THEN RETURN SUBSTRING(phone_number FROM 3); -- South Korea
    ELSIF phone_number LIKE '34%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Spain
    ELSIF phone_number LIKE '94%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Sri Lanka
    ELSIF phone_number LIKE '46%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Sweden
    ELSIF phone_number LIKE '41%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Switzerland
    ELSIF phone_number LIKE '66%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Thailand
    ELSIF phone_number LIKE '90%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Turkey
    ELSIF phone_number LIKE '44%' THEN RETURN SUBSTRING(phone_number FROM 3); -- United Kingdom
    ELSIF phone_number LIKE '58%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Venezuela
    ELSIF phone_number LIKE '84%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Vietnam
    ELSIF phone_number LIKE '95%' THEN RETURN SUBSTRING(phone_number FROM 3); -- Myanmar
    ELSIF phone_number LIKE '351%' THEN RETURN SUBSTRING(phone_number FROM 4); -- Portugal

    -- 1-digit country codes (handle these last to avoid conflicts)
    ELSIF phone_number LIKE '1%' THEN RETURN SUBSTRING(phone_number FROM 2); -- USA/Canada (and territories)
    ELSIF phone_number LIKE '7%' THEN RETURN SUBSTRING(phone_number FROM 2); -- Russia/Kazakhstan

    -- If no country code matches, return the original number
    ELSE RETURN phone_number;
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;