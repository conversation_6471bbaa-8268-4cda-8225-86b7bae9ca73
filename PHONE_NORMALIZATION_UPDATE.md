# International Phone Number Normalization Update

This update adds comprehensive international phone number normalization support to the Contact Times app, handling all international country codes for proper phone number matching.

## Files Updated

### 1. `country_codes.sql` (NEW FILE)
- Contains a comprehensive SQL function `normalize_phone_number()` that handles all international country codes
- Supports 4-digit, 3-digit, 2-digit, and 1-digit country codes
- Handles over 200 countries and territories

### 2. `policy_updates.sql` (UPDATED)
- Updated RLS policies to use the new `normalize_phone_number()` SQL function
- Fixes phone number matching issues in database policies
- Enables proper cross-user contact categorization viewing

### 3. `lib/services/contacts_service.dart` (UPDATED)
- Updated Dart `normalizePhoneForMatching()` function to match SQL function
- Handles all international country codes consistently
- Maintains consistency between client-side and server-side normalization

## Installation Instructions

### Step 1: Execute SQL Functions and Policies
1. **Go to your Supabase dashboard**
2. **Navigate to SQL Editor**
3. **Execute the country codes SQL first:**
   - Copy and paste the entire content of `country_codes.sql`
   - Click "Run" to create the normalization function

4. **Execute the updated policies:**
   - Copy and paste the entire content of `policy_updates.sql`
   - Click "Run" to update the RLS policies

### Step 2: Test the Application
1. **Hot reload your Flutter app** (the Dart code has already been updated)
2. **Test phone number matching** with different formats:
   - `+967775515722` (with country code and +)
   - `967775515722` (with country code, no +)
   - `775515722` (without country code)

## What This Fixes

### Before:
- Only handled Yemen country code (967)
- Phone number matching failed for international numbers
- RLS policies couldn't match phone numbers in different formats
- Users couldn't see categories assigned by others

### After:
- Handles all international country codes (200+ countries)
- Consistent phone number normalization across client and server
- Proper RLS policy matching for all phone number formats
- Cross-user contact categorization works correctly

## Supported Country Codes

The normalization now handles country codes for:
- **4-digit codes**: US territories and Caribbean (e.g., 1684, 1264, 1268...)
- **3-digit codes**: Most countries (e.g., 355, 213, 376, 967...)
- **2-digit codes**: Major countries (e.g., 93, 54, 61, 44...)
- **1-digit codes**: US/Canada (1) and Russia/Kazakhstan (7)

## Testing Scenarios

After applying these updates, test these scenarios:

1. **User A categorizes User B's phone number**
2. **User B views User A's contact details**
3. **Should see the category that User A assigned to User B**
4. **Test with different phone number formats** (+country code, without +, without country code)

## Troubleshooting

If the feature still doesn't work after applying these updates:

1. **Check SQL execution**: Ensure both SQL files executed without errors
2. **Verify function creation**: Run `SELECT normalize_phone_number('+967775515722');` in SQL editor
3. **Check policy updates**: Verify the policies were updated (no duplicate policy errors)
4. **Test normalization**: Check that both client and server normalize the same way

## Notes

- The normalization function is marked as `IMMUTABLE` for better performance
- Country codes are ordered by length to avoid partial matches
- Minimum phone length validation (7 digits) prevents over-normalization
- Both SQL and Dart functions use identical logic for consistency
