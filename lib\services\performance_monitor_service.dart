// ignore_for_file: avoid_print

import 'dart:async';
import 'package:flutter/foundation.dart';

/// Service to monitor performance metrics for the contacts app
/// Tracks loading times, cache hits, and sync efficiency
class PerformanceMonitorService extends ChangeNotifier {
  static final PerformanceMonitorService _instance = PerformanceMonitorService._internal();
  factory PerformanceMonitorService() => _instance;
  PerformanceMonitorService._internal();

  // Performance metrics
  final Map<String, DateTime> _operationStartTimes = {};
  final Map<String, Duration> _operationDurations = {};
  final Map<String, int> _operationCounts = {};
  final Map<String, int> _cacheHits = {};
  final Map<String, int> _cacheMisses = {};
  final List<PerformanceMetric> _metrics = [];

  // Getters for metrics
  Map<String, Duration> get operationDurations => Map.from(_operationDurations);
  Map<String, int> get operationCounts => Map.from(_operationCounts);
  Map<String, int> get cacheHits => Map.from(_cacheHits);
  Map<String, int> get cacheMisses => Map.from(_cacheMisses);
  List<PerformanceMetric> get metrics => List.from(_metrics);

  // Start timing an operation
  void startOperation(String operationName) {
    _operationStartTimes[operationName] = DateTime.now();
    print('⏱️ Started: $operationName');
  }

  // End timing an operation
  void endOperation(String operationName) {
    final startTime = _operationStartTimes[operationName];
    if (startTime != null) {
      final duration = DateTime.now().difference(startTime);
      _operationDurations[operationName] = duration;
      _operationCounts[operationName] = (_operationCounts[operationName] ?? 0) + 1;
      
      // Add to metrics history
      _metrics.add(PerformanceMetric(
        operationName: operationName,
        duration: duration,
        timestamp: DateTime.now(),
        type: MetricType.operation,
      ));

      print('⏱️ Completed: $operationName in ${duration.inMilliseconds}ms');
      
      // Keep only last 100 metrics to prevent memory issues
      if (_metrics.length > 100) {
        _metrics.removeAt(0);
      }
      
      _operationStartTimes.remove(operationName);
      notifyListeners();
    }
  }

  // Record cache hit
  void recordCacheHit(String cacheType) {
    _cacheHits[cacheType] = (_cacheHits[cacheType] ?? 0) + 1;
    
    _metrics.add(PerformanceMetric(
      operationName: '$cacheType-cache-hit',
      duration: Duration.zero,
      timestamp: DateTime.now(),
      type: MetricType.cacheHit,
    ));
    
    print('💾 Cache hit: $cacheType');
    notifyListeners();
  }

  // Record cache miss
  void recordCacheMiss(String cacheType) {
    _cacheMisses[cacheType] = (_cacheMisses[cacheType] ?? 0) + 1;
    
    _metrics.add(PerformanceMetric(
      operationName: '$cacheType-cache-miss',
      duration: Duration.zero,
      timestamp: DateTime.now(),
      type: MetricType.cacheMiss,
    ));
    
    print('💾 Cache miss: $cacheType');
    notifyListeners();
  }

  // Get cache hit ratio for a specific cache type
  double getCacheHitRatio(String cacheType) {
    final hits = _cacheHits[cacheType] ?? 0;
    final misses = _cacheMisses[cacheType] ?? 0;
    final total = hits + misses;
    
    if (total == 0) return 0.0;
    return hits / total;
  }

  // Get average operation duration
  Duration? getAverageOperationDuration(String operationName) {
    final count = _operationCounts[operationName] ?? 0;
    if (count == 0) return null;
    
    final totalDuration = _metrics
        .where((m) => m.operationName == operationName && m.type == MetricType.operation)
        .fold<Duration>(Duration.zero, (sum, metric) => sum + metric.duration);
    
    return Duration(milliseconds: totalDuration.inMilliseconds ~/ count);
  }

  // Get performance summary
  PerformanceSummary getPerformanceSummary() {
    final now = DateTime.now();
    final last5Minutes = now.subtract(const Duration(minutes: 5));
    
    final recentMetrics = _metrics.where((m) => m.timestamp.isAfter(last5Minutes)).toList();
    
    final operationMetrics = recentMetrics.where((m) => m.type == MetricType.operation).toList();
    final cacheHitMetrics = recentMetrics.where((m) => m.type == MetricType.cacheHit).toList();
    final cacheMissMetrics = recentMetrics.where((m) => m.type == MetricType.cacheMiss).toList();
    
    final avgLoadTime = operationMetrics.isNotEmpty
        ? Duration(milliseconds: operationMetrics
            .map((m) => m.duration.inMilliseconds)
            .reduce((a, b) => a + b) ~/ operationMetrics.length)
        : Duration.zero;
    
    final totalCacheRequests = cacheHitMetrics.length + cacheMissMetrics.length;
    final cacheHitRatio = totalCacheRequests > 0 
        ? cacheHitMetrics.length / totalCacheRequests 
        : 0.0;
    
    return PerformanceSummary(
      averageLoadTime: avgLoadTime,
      totalOperations: operationMetrics.length,
      cacheHitRatio: cacheHitRatio,
      totalCacheRequests: totalCacheRequests,
      timeWindow: const Duration(minutes: 5),
    );
  }

  // Clear all metrics
  void clearMetrics() {
    _operationStartTimes.clear();
    _operationDurations.clear();
    _operationCounts.clear();
    _cacheHits.clear();
    _cacheMisses.clear();
    _metrics.clear();
    print('📊 Performance metrics cleared');
    notifyListeners();
  }

  // Log performance summary
  void logPerformanceSummary() {
    final summary = getPerformanceSummary();
    print('📊 Performance Summary (last 5 minutes):');
    print('   Average load time: ${summary.averageLoadTime.inMilliseconds}ms');
    print('   Total operations: ${summary.totalOperations}');
    print('   Cache hit ratio: ${(summary.cacheHitRatio * 100).toStringAsFixed(1)}%');
    print('   Total cache requests: ${summary.totalCacheRequests}');
  }
}

class PerformanceMetric {
  final String operationName;
  final Duration duration;
  final DateTime timestamp;
  final MetricType type;

  PerformanceMetric({
    required this.operationName,
    required this.duration,
    required this.timestamp,
    required this.type,
  });
}

enum MetricType {
  operation,
  cacheHit,
  cacheMiss,
}

class PerformanceSummary {
  final Duration averageLoadTime;
  final int totalOperations;
  final double cacheHitRatio;
  final int totalCacheRequests;
  final Duration timeWindow;

  PerformanceSummary({
    required this.averageLoadTime,
    required this.totalOperations,
    required this.cacheHitRatio,
    required this.totalCacheRequests,
    required this.timeWindow,
  });
}

// Extension to easily add performance monitoring to any operation
extension PerformanceMonitoring on Future<T> Function<T>() {
  Future<T> withPerformanceMonitoring<T>(String operationName) async {
    final monitor = PerformanceMonitorService();
    monitor.startOperation(operationName);
    try {
      final result = await this();
      monitor.endOperation(operationName);
      return result;
    } catch (e) {
      monitor.endOperation(operationName);
      rethrow;
    }
  }
}
