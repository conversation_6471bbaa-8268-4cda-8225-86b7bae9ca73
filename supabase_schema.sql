-- Contact Times App Database Schema
-- This file contains the database schema for the Contact Times Flutter app
-- Run this in your Supabase SQL editor to set up the database

-- Note: JWT secret is automatically managed by Supabase, no need to set it manually

-- Create storage bucket for profile pictures
INSERT INTO storage.buckets (id, name, public)
VALUES ('profile-pictures', 'profile-pictures', true)
ON CONFLICT (id) DO NOTHING;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can upload their own profile pictures" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own profile pictures" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own profile pictures" ON storage.objects;
DROP POLICY IF EXISTS "Profile pictures are publicly viewable" ON storage.objects;

-- Set up storage policies for profile pictures
-- Allow users to upload files in their own folder (userId/filename)
CREATE POLICY "Users can upload their own profile pictures" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'profile-pictures'
  AND auth.uid()::text = split_part(name, '/', 1)
);

-- Allow users to update files in their own folder
CREATE POLICY "Users can update their own profile pictures" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'profile-pictures'
  AND auth.uid()::text = split_part(name, '/', 1)
);

-- Allow users to delete files in their own folder
CREATE POLICY "Users can delete their own profile pictures" ON storage.objects
FOR DELETE USING (
  bucket_id = 'profile-pictures'
  AND auth.uid()::text = split_part(name, '/', 1)
);

-- Allow public viewing of all profile pictures
CREATE POLICY "Profile pictures are publicly viewable" ON storage.objects
FOR SELECT USING (bucket_id = 'profile-pictures');

-- Migration: Add preferred_language column to existing profiles table
-- Run this if you already have the profiles table without the preferred_language column
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'profiles'
        AND column_name = 'preferred_language'
    ) THEN
        ALTER TABLE public.profiles
        ADD COLUMN preferred_language TEXT DEFAULT 'en' CHECK (preferred_language IN ('en', 'ar'));
    END IF;
END $$;

-- Create profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE,
    full_name TEXT,
    avatar_url TEXT,
    phone_number TEXT UNIQUE NOT NULL,
    preferred_language TEXT DEFAULT 'en' CHECK (preferred_language IN ('en', 'ar')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create categories table
CREATE TABLE IF NOT EXISTS public.categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    type INTEGER NOT NULL CHECK (type IN (1, 2, 3, 4)),
    note TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create time_slots table
CREATE TABLE IF NOT EXISTS public.time_slots (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    category_id UUID REFERENCES public.categories(id) ON DELETE CASCADE NOT NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_contacts table
CREATE TABLE IF NOT EXISTS public.user_contacts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    categorized_contact_phone TEXT NOT NULL,
    assigned_category_id UUID REFERENCES public.categories(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, categorized_contact_phone)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_phone_number ON public.profiles(phone_number);
CREATE INDEX IF NOT EXISTS idx_categories_user_id ON public.categories(user_id);
CREATE INDEX IF NOT EXISTS idx_time_slots_category_id ON public.time_slots(category_id);
CREATE INDEX IF NOT EXISTS idx_user_contacts_user_id ON public.user_contacts(user_id);
CREATE INDEX IF NOT EXISTS idx_user_contacts_phone ON public.user_contacts(categorized_contact_phone);

-- Enable Row Level Security (RLS)
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_slots ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_contacts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Profiles policies
CREATE POLICY "Users can view their own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Categories policies
CREATE POLICY "Users can view their own categories" ON public.categories
    FOR SELECT USING (auth.uid() = user_id);

-- Allow users to view categories that were assigned to them by others
-- This enables User B to see categories that User A assigned to them
CREATE POLICY "Users can view categories assigned to them" ON public.categories
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_contacts
            JOIN public.profiles ON profiles.id = auth.uid()
            WHERE user_contacts.assigned_category_id = categories.id
            AND profiles.phone_number = user_contacts.categorized_contact_phone
        )
    );

CREATE POLICY "Users can insert their own categories" ON public.categories
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own categories" ON public.categories
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own categories" ON public.categories
    FOR DELETE USING (auth.uid() = user_id);

-- Time slots policies
CREATE POLICY "Users can view time slots for their categories" ON public.time_slots
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.categories
            WHERE categories.id = time_slots.category_id
            AND categories.user_id = auth.uid()
        )
    );

-- Allow users to view time slots for categories that were assigned to them
-- This enables User B to see time slots for categories that User A assigned to them
CREATE POLICY "Users can view time slots for categories assigned to them" ON public.time_slots
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.categories
            JOIN public.user_contacts ON user_contacts.assigned_category_id = categories.id
            JOIN public.profiles ON profiles.id = auth.uid()
            WHERE categories.id = time_slots.category_id
            AND profiles.phone_number = user_contacts.categorized_contact_phone
        )
    );

CREATE POLICY "Users can insert time slots for their categories" ON public.time_slots
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.categories 
            WHERE categories.id = time_slots.category_id 
            AND categories.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update time slots for their categories" ON public.time_slots
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.categories 
            WHERE categories.id = time_slots.category_id 
            AND categories.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete time slots for their categories" ON public.time_slots
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.categories 
            WHERE categories.id = time_slots.category_id 
            AND categories.user_id = auth.uid()
        )
    );

-- User contacts policies
CREATE POLICY "Users can view their own contacts" ON public.user_contacts
    FOR SELECT USING (auth.uid() = user_id);

-- Allow users to view contact assignments where they are the categorized contact
-- This enables User B to see how User A categorized them
CREATE POLICY "Users can view assignments where they are the contact" ON public.user_contacts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.id = auth.uid()
            AND profiles.phone_number = user_contacts.categorized_contact_phone
        )
    );

CREATE POLICY "Users can insert their own contacts" ON public.user_contacts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own contacts" ON public.user_contacts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own contacts" ON public.user_contacts
    FOR DELETE USING (auth.uid() = user_id);

-- Allow users to view profiles by phone number (for finding contacts who use the app)
CREATE POLICY "Users can view profiles by phone number" ON public.profiles
    FOR SELECT USING (true);

-- Function to automatically create default categories for new users
CREATE OR REPLACE FUNCTION create_default_categories()
RETURNS TRIGGER AS $$
BEGIN
    -- Category 1: Contact Me Anytime
    INSERT INTO public.categories (user_id, type, note)
    VALUES (NEW.id, 1, 'You can contact me at any time.');
    
    -- Category 2: Prefer to be Contacted Anytime (with suggestions)
    INSERT INTO public.categories (user_id, type, note)
    VALUES (NEW.id, 2, 'You can contact me at any time, but I''d prefer if you use these times if the call can wait.');
    
    -- Category 3: Contact Me At These Times
    INSERT INTO public.categories (user_id, type, note)
    VALUES (NEW.id, 3, 'Please contact me at these times.');
    
    -- Category 4: Contact Me Through Messages
    INSERT INTO public.categories (user_id, type, note)
    VALUES (NEW.id, 4, 'Please contact me through messages at the following times.');
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create default categories when a new profile is created
CREATE TRIGGER create_default_categories_trigger
    AFTER INSERT ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION create_default_categories();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers to update updated_at timestamp
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at
    BEFORE UPDATE ON public.categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_contacts_updated_at
    BEFORE UPDATE ON public.user_contacts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
