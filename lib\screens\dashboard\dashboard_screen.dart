import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../contacts/contact_distribution_screen.dart';
import '../contacts/contacts_screen.dart';
import '../categories/categories_screen.dart';
import '../profile/profile_screen.dart';
import '../../services/notification_service.dart';
import '../../services/supabase_service.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _currentIndex = 0;
  bool _isCheckingContacts = true;

  @override
  void initState() {
    super.initState();
    // Check for pending navigation from notification taps
    WidgetsBinding.instance.addPostFrameCallback((_) {
      NotificationService.checkForPendingNavigation();
    });
    _checkUserContactsAndRedirect();
  }

  Future<void> _checkUserContactsAndRedirect() async {
    try {
      final user = SupabaseService.currentUser;
      if (user == null) return;

      // Check if user has any contacts
      final userContacts = await SupabaseService.getUserContacts(user.id);

      if (mounted) {
        setState(() {
          _isCheckingContacts = false;
          // If user has contacts, start with contacts page (index 0)
          // If no contacts, start with distribute page (index 2)
          _currentIndex = userContacts.isNotEmpty ? 0 : 2;
        });
      }
    } catch (e) {
      print('Error checking user contacts: $e');
      if (mounted) {
        setState(() {
          _isCheckingContacts = false;
          // Default to distribute page if error
          _currentIndex = 2;
        });
      }
    }
  }

  final List<Widget> _screens = [
    const ContactsScreen(),        // Index 0 - Contacts tab
    const CategoriesScreen(),      // Index 1 - Categories tab
    const ContactDistributionScreen(), // Index 2 - Distribute tab
    const ProfileScreen(),         // Index 3 - Profile tab
  ];

  @override
  Widget build(BuildContext context) {
    // Show loading indicator while checking contacts
    if (_isCheckingContacts) {
      return Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF667eea),
                Color(0xFF764ba2),
              ],
            ),
          ),
          child: const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
        ),
      );
    }

    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.topRight,
            colors: [
              const Color(0xFF667eea).withOpacity(0.95),
              const Color(0xFF764ba2).withOpacity(0.95),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, -5),
              spreadRadius: 0,
            ),
          ],
        ),
        child: SafeArea(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNavItem(
                  icon: Icons.contacts_rounded,
                  label: AppLocalizations.of(context)?.contacts ?? 'Contacts',
                  index: 0,
                ),
                _buildNavItem(
                  icon: Icons.category_rounded,
                  label: AppLocalizations.of(context)?.categories ?? 'Categories',
                  index: 1,
                ),
                _buildNavItem(
                  icon: Icons.swipe_rounded,
                  label: AppLocalizations.of(context)?.distribute ?? 'Distribute',
                  index: 2,
                ),
                _buildNavItem(
                  icon: Icons.person_rounded,
                  label: AppLocalizations.of(context)?.profile ?? 'Profile',
                  index: 3,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required int index,
  }) {
    final isSelected = _currentIndex == index;

    return GestureDetector(
      onTap: () {
        setState(() {
          _currentIndex = index;
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected
              ? Colors.white.withOpacity(0.2)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: isSelected
              ? Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                )
              : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: isSelected ? 24 : 20,
            ),
            const SizedBox(height: 2),
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 200),
              style: TextStyle(
                color: Colors.white,
                fontSize: isSelected ? 11 : 9,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
              child: Text(label),
            ),
          ],
        ),
      ),
    );
  }
}










