# Deploy Edge Function for Account Deletion

## Prerequisites

1. Install Supabase CLI:
   ```bash
   npm install -g supabase
   ```

2. Login to Supabase:
   ```bash
   supabase login
   ```

## Deploy the Edge Function

1. Navigate to your project directory:
   ```bash
   cd "c:\Users\<USER>\Desktop\contact times\contact_times"
   ```

2. Deploy the function (replace YOUR_PROJECT_REF with your actual project reference):
   ```bash
   supabase functions deploy delete_user_account --project-ref YOUR_PROJECT_REF
   ```

   You can find your project reference in your Supabase dashboard URL:
   `https://supabase.com/dashboard/project/YOUR_PROJECT_REF`

## Update Flutter Code to Use Edge Function

After deploying the Edge Function, you can update the `deleteAccount()` method in `SupabaseService` to use the Edge Function instead:

```dart
// Account deletion using Edge Function
static Future<void> deleteAccount() async {
  try {
    final user = currentUser;
    if (user == null) throw Exception('No user logged in');

    print('🗑️ Starting account deletion for user: ${user.id}');

    // Call the Edge Function
    final response = await client.functions.invoke('delete_user_account');

    if (response.status != 200) {
      throw Exception('Failed to delete account: ${response.data}');
    }

    print('🗑️ Account deletion completed successfully');
  } catch (e) {
    print('❌ Error deleting account: $e');
    rethrow;
  }
}
```

## Benefits of Using Edge Function

1. **Complete Deletion**: Deletes both app data AND auth user record
2. **Secure**: Uses SERVICE_ROLE key on server side, not exposed in app
3. **Reliable**: Proper error handling and transaction-like behavior
4. **GDPR Compliant**: Completely removes user from the system

## Current Implementation vs Edge Function

**Current Implementation (Immediate Fix):**
- ✅ Deletes all app data (profile, categories, contacts, etc.)
- ✅ Signs out user
- ❌ Leaves auth user record in Supabase
- ✅ Works immediately without additional setup

**Edge Function Implementation:**
- ✅ Deletes all app data
- ✅ Deletes auth user record completely
- ✅ More secure and proper
- ❌ Requires Edge Function deployment

Both implementations will solve your immediate 403 error. The current fix will work right away, and you can upgrade to the Edge Function later for complete deletion.
