import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/models.dart';
import '../services/contacts_service.dart';

class CallingService {
  static final FlutterLocalNotificationsPlugin _notifications = 
      FlutterLocalNotificationsPlugin();

  static Future<void> initialize() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings();
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(initSettings);
  }

  static Future<void> makeCall({
    required Profile contactProfile,
    required Category? contactCategory,
    required String callerName,
    String? specificPhoneNumber, // Optional: use this phone number instead of profile's phone number
  }) async {
    // 1. Show local notification to caller
    await _showCallerNotification(contactProfile, contactCategory);

    // 2. Send push notification to contact (would be implemented with Supabase Edge Functions)
    await _sendContactNotification(contactProfile, callerName, contactCategory);

    // 3. Launch phone dialer
    final phoneToCall = specificPhoneNumber ?? contactProfile.phoneNumber;
    await _launchPhoneDialer(phoneToCall);
  }

  static Future<void> _showCallerNotification(
    Profile contactProfile, 
    Category? contactCategory,
  ) async {
    String title;
    String body;

    if (contactCategory == null) {
      title = 'Calling ${contactProfile.fullName ?? contactProfile.phoneNumber}';
      body = 'This contact hasn\'t set up Contact Times yet.';
    } else {
      final isGoodTime = _isGoodTimeToCall(contactCategory);
      
      if (isGoodTime) {
        title = 'Good time to call!';
        body = 'Now is a good time to call ${contactProfile.fullName ?? contactProfile.phoneNumber}.';
      } else {
        title = 'Outside preferred time';
        body = 'This is outside of ${contactProfile.fullName ?? contactProfile.phoneNumber}\'s preferred time. Do you want to continue?';
      }
    }

    const androidDetails = AndroidNotificationDetails(
      'calling_channel',
      'Calling Notifications',
      channelDescription: 'Notifications for calling actions',
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails();

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      0,
      title,
      body,
      notificationDetails,
    );
  }

  static Future<void> _sendContactNotification(
    Profile contactProfile,
    String callerName,
    Category? contactCategory,
  ) async {
    // TODO: Implement actual push notification using Supabase Edge Functions
    // This would involve calling a Supabase function that sends a push notification
    // to the contact's device using their FCM token

    // The notification message would be constructed as follows:
    // if (contactCategory != null) {
    //   final isGoodTime = _isGoodTimeToCall(contactCategory);
    //   final categoryName = contactCategory.type.displayName;
    //
    //   if (isGoodTime) {
    //     message = '$callerName ($categoryName) is calling. They are calling at the correct time.';
    //   } else {
    //     message = '$callerName ($categoryName) is calling at an inconvenient time.';
    //   }
    // } else {
    //   message = '$callerName is calling.';
    // }
  }

  static Future<void> _launchPhoneDialer(String phoneNumber) async {
    final cleanedNumber = ContactsService.cleanPhoneNumber(phoneNumber);
    final uri = Uri.parse('tel:$cleanedNumber');

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      throw Exception('Could not launch phone dialer');
    }
  }

  /// Launch phone dialer with the given phone number (public method)
  static Future<void> launchDialer(String phoneNumber) async {
    await _launchPhoneDialer(phoneNumber);
  }

  /// Send SMS with the given message to the phone number
  static Future<void> sendSMS(String phoneNumber, String message) async {
    try {
      final cleanedNumber = ContactsService.cleanPhoneNumber(phoneNumber);
      print('📱 Attempting to send SMS to: $cleanedNumber');

      // Try different SMS URI formats for better compatibility
      final uriFormats = [
        'sms:$cleanedNumber?body=${Uri.encodeComponent(message)}',
        'sms:$cleanedNumber&body=${Uri.encodeComponent(message)}',
        'sms:$cleanedNumber',
      ];

      bool launched = false;
      for (final uriString in uriFormats) {
        try {
          final uri = Uri.parse(uriString);
          print('📱 Trying SMS URI: $uriString');

          if (await canLaunchUrl(uri)) {
            await launchUrl(uri, mode: LaunchMode.externalApplication);
            launched = true;
            print('✅ SMS app launched successfully');
            break;
          }
        } catch (e) {
          print('❌ Failed to launch SMS with URI $uriString: $e');
          continue;
        }
      }

      if (!launched) {
        throw Exception('Could not launch SMS app. Please check if SMS app is available on your device.');
      }
    } catch (e) {
      print('❌ SMS sending failed: $e');
      rethrow;
    }
  }

  static bool _isGoodTimeToCall(Category category) {
    if (category.type == CategoryType.contactAnytime) {
      return true;
    }

    if (category.timeSlots.isEmpty) {
      return category.type == CategoryType.preferAnytime;
    }

    // Check if current time falls within any of the time slots
    return category.timeSlots.any((slot) => slot.isCurrentTimeInSlot());
  }

  static String getCallingSuggestion(Category? category, BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    if (category == null) {
      return l10n.callingSuggestionNoCategory;
    }

    switch (category.type) {
      case CategoryType.contactAnytime:
        return l10n.callingSuggestionContactAnytime;

      case CategoryType.preferAnytime:
        if (category.timeSlots.isEmpty) {
          return l10n.callingSuggestionContactAnytime;
        }
        final isGoodTime = _isGoodTimeToCall(category);
        if (isGoodTime) {
          return l10n.callingSuggestionPreferAnytimeGoodTime;
        } else {
          return l10n.callingSuggestionPreferAnytimeBadTime;
        }

      case CategoryType.contactAtTimes:
        final isGoodTime = _isGoodTimeToCall(category);
        if (isGoodTime) {
          return l10n.callingSuggestionContactAtTimesGoodTime;
        } else {
          return l10n.callingSuggestionContactAtTimesBadTime;
        }

      case CategoryType.contactThroughMessages:
        return l10n.callingSuggestionContactThroughMessages;
    }
  }

  static List<TimeSlot> getNextAvailableSlots(Category category) {
    if (category.timeSlots.isEmpty) return [];

    final now = DateTime.now();
    final currentDay = now.weekday % 7;
    final currentTime = CustomTimeOfDay(hour: now.hour, minute: now.minute);

    // Find slots for today that are still upcoming
    final todaySlots = category.timeSlots
        .where((slot) =>
            slot.dayOfWeek == currentDay &&
            currentTime.isBeforeOrEqual(slot.startTime))
        .toList();

    if (todaySlots.isNotEmpty) {
      return todaySlots;
    }

    // Find slots for the next few days
    final upcomingSlots = <TimeSlot>[];
    for (int i = 1; i <= 7; i++) {
      final targetDay = (currentDay + i) % 7;
      final daySlots = category.timeSlots
          .where((slot) => slot.dayOfWeek == targetDay)
          .toList();

      if (daySlots.isNotEmpty) {
        upcomingSlots.addAll(daySlots);
        if (upcomingSlots.length >= 3) break; // Limit to next 3 slots
      }
    }

    return upcomingSlots.take(3).toList();
  }
}
