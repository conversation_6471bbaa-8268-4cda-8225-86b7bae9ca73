import 'package:flutter_test/flutter_test.dart';
import 'package:contact_times/services/contacts_service.dart';

void main() {
  group('Universal Phone Number Matching Tests', () {
    test('should match Yemen numbers with different formats', () {
      // Yemen country code: 967
      expect(ContactsService.doPhoneNumbersMatch('775515722', '+967775515722'), true);
      expect(ContactsService.doPhoneNumbersMatch('967775515722', '775515722'), true);
      expect(ContactsService.doPhoneNumbersMatch('+967775515722', '967775515722'), true);
    });

    test('should match US numbers with different formats', () {
      // US country code: 1
      expect(ContactsService.doPhoneNumbersMatch('5551234567', '+15551234567'), true);
      expect(ContactsService.doPhoneNumbersMatch('15551234567', '5551234567'), true);
      expect(ContactsService.doPhoneNumbersMatch('+15551234567', '15551234567'), true);
    });

    test('should match UK numbers with different formats', () {
      // UK country code: 44
      expect(ContactsService.doPhoneNumbersMatch('7700900123', '+447700900123'), true);
      expect(ContactsService.doPhoneNumbersMatch('447700900123', '7700900123'), true);
      expect(ContactsService.doPhoneNumbersMatch('+447700900123', '447700900123'), true);
    });

    test('should match German numbers with different formats', () {
      // Germany country code: 49
      expect(ContactsService.doPhoneNumbersMatch('1701234567', '+491701234567'), true);
      expect(ContactsService.doPhoneNumbersMatch('491701234567', '1701234567'), true);
      expect(ContactsService.doPhoneNumbersMatch('+491701234567', '491701234567'), true);
    });

    test('should match Egyptian numbers with different formats', () {
      // Egypt country code: 20
      expect(ContactsService.doPhoneNumbersMatch('1012345678', '+201012345678'), true);
      expect(ContactsService.doPhoneNumbersMatch('201012345678', '1012345678'), true);
      expect(ContactsService.doPhoneNumbersMatch('+201012345678', '201012345678'), true);
    });

    test('should match Saudi numbers with different formats', () {
      // Saudi Arabia country code: 966
      expect(ContactsService.doPhoneNumbersMatch('501234567', '+966501234567'), true);
      expect(ContactsService.doPhoneNumbersMatch('966501234567', '501234567'), true);
      expect(ContactsService.doPhoneNumbersMatch('+966501234567', '966501234567'), true);
    });

    test('should handle numbers with leading zeros', () {
      expect(ContactsService.doPhoneNumbersMatch('0775515722', '775515722'), true);
      expect(ContactsService.doPhoneNumbersMatch('0775515722', '+967775515722'), true);
    });

    test('should not match completely different numbers', () {
      expect(ContactsService.doPhoneNumbersMatch('775515722', '123456789'), false);
      expect(ContactsService.doPhoneNumbersMatch('+967775515722', '+15551234567'), false);
    });

    test('should generate appropriate variations for different countries', () {
      // Test Yemen number
      final yemenVariations = ContactsService.generatePhoneVariations('775515722');
      expect(yemenVariations.contains('775515722'), true);
      expect(yemenVariations.contains('+775515722'), true);

      // Test US number
      final usVariations = ContactsService.generatePhoneVariations('5551234567');
      expect(usVariations.contains('5551234567'), true);
      expect(usVariations.contains('+5551234567'), true);

      // Test number with country code
      final withCountryCode = ContactsService.generatePhoneVariations('+967775515722');
      expect(withCountryCode.contains('967775515722'), true);
      expect(withCountryCode.contains('775515722'), true);
      expect(withCountryCode.contains('+967775515722'), true);
    });

    test('should handle edge cases', () {
      // Empty strings
      expect(ContactsService.doPhoneNumbersMatch('', '123456789'), false);
      expect(ContactsService.doPhoneNumbersMatch('123456789', ''), false);
      expect(ContactsService.doPhoneNumbersMatch('', ''), false);

      // Very short numbers
      expect(ContactsService.generatePhoneVariations('123').length, greaterThan(0));
      
      // Numbers with special characters (should be cleaned)
      expect(ContactsService.doPhoneNumbersMatch('+967-775-515-722', '967775515722'), true);
      expect(ContactsService.doPhoneNumbersMatch('(967) 775-515-722', '+967775515722'), true);
    });
  });
}
