// ignore_for_file: prefer_const_constructors

import 'dart:convert';
import 'dart:math' as math;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../models/models.dart';
import '../../models/category_extensions.dart';
import '../../services/contacts_service.dart';
import '../../services/supabase_service.dart';
import '../../widgets/no_internet_widget.dart';

class ContactDistributionScreen extends StatefulWidget {
  const ContactDistributionScreen({super.key});

  @override
  State<ContactDistributionScreen> createState() =>
      _ContactDistributionScreenState();
}

class _ContactDistributionScreenState extends State<ContactDistributionScreen> {
  final CardSwiperController _controller = CardSwiperController();
  List<DeviceContact> _uncategorizedContacts = [];
  List<Category> _categories = [];
  bool _isLoading = true;
  String? _error;
  int _currentIndex = 0;
  Set<String> _assignedContactIds = {}; // Track assigned contacts
  Map<String, String> _contactCategoryAssignments = {}; // contactId -> categoryId
  static const String _positionKey = 'contact_distribution_position';
  static const String _assignedContactsKey = 'assigned_contact_ids';

  // A-Z Navigation
  bool _showAlphabetDropdown = false;
  String? _selectedLetter;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _saveCurrentPosition() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_positionKey, _currentIndex);
    } catch (e) {
      print('Error saving position: $e');
    }
  }

  Future<void> _loadSavedPositionBeforeData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedPosition = prefs.getInt(_positionKey) ?? 0;
      if (mounted) {
        setState(() {
          _currentIndex = savedPosition;
        });
      }
    } catch (e) {
      print('Error loading position: $e');
    }
  }

  Future<void> _loadSavedPosition() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedPosition = prefs.getInt(_positionKey) ?? 0;
      if (mounted && savedPosition < _uncategorizedContacts.length) {
        setState(() {
          _currentIndex = savedPosition;
        });
        // Move to saved position after ensuring CardSwiper is ready
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Add a small delay to ensure CardSwiper is fully initialized
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted && _currentIndex < _uncategorizedContacts.length) {
              _controller.moveTo(_currentIndex);
            }
          });
        });
      }
    } catch (e) {
      print('Error loading position: $e');
    }
  }

  Future<void> _saveAssignedContacts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_assignedContactsKey, _assignedContactIds.toList());
    } catch (e) {
      print('Error saving assigned contacts: $e');
    }
  }

  Future<void> _loadAssignedContacts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final assignedList = prefs.getStringList(_assignedContactsKey) ?? [];
      _assignedContactIds = assignedList.toSet();
    } catch (e) {
      print('Error loading assigned contacts: $e');
    }
  }

  void _moveToNextContact() {
    if (_currentIndex < _uncategorizedContacts.length - 1) {
      setState(() {
        _currentIndex++;
      });
      _controller.moveTo(_currentIndex);
      _saveCurrentPosition(); // Save position when navigating
    }
  }

  void _moveToPreviousContact() {
    if (_currentIndex > 0) {
      setState(() {
        _currentIndex--;
      });
      _controller.moveTo(_currentIndex);
      _saveCurrentPosition(); // Save position when navigating
    }
  }

  void _onCardSwiped(int previousIndex, int? currentIndex) {
    if (currentIndex != null && currentIndex < _uncategorizedContacts.length) {
      setState(() {
        _currentIndex = currentIndex;
      });
      _saveCurrentPosition();
    }
  }

  // A-Z Navigation Methods
  void _toggleAlphabetDropdown() {
    setState(() {
      _showAlphabetDropdown = !_showAlphabetDropdown;
    });
  }

  void _jumpToLetter(String letter) {
    final targetIndex = _uncategorizedContacts.indexWhere((contact) {
      final firstLetter = contact.displayName.isNotEmpty
          ? contact.displayName[0].toUpperCase()
          : 'A';
      return firstLetter == letter;
    });

    if (targetIndex != -1) {
      setState(() {
        _currentIndex = targetIndex;
        _selectedLetter = letter;
        _showAlphabetDropdown = false;
      });
      _controller.moveTo(targetIndex);
      _saveCurrentPosition();
    } else {
      // If no contact found for this letter, close dropdown
      setState(() {
        _showAlphabetDropdown = false;
      });
    }
  }

  List<String> _getAvailableLetters() {
    final letters = <String>{};
    for (final contact in _uncategorizedContacts) {
      if (contact.displayName.isNotEmpty) {
        final firstLetter = contact.displayName[0].toUpperCase();
        // Include both English letters (A-Z) and Arabic letters (ا-ي)
        if (RegExp(r'[A-Z]').hasMatch(firstLetter) ||
            RegExp(r'[\u0600-\u06FF]').hasMatch(firstLetter)) {
          letters.add(firstLetter);
        }
      }
    }

    // Sort letters: Arabic first, then English
    final sortedLetters = letters.toList()..sort((a, b) {
      final isArabicA = RegExp(r'[\u0600-\u06FF]').hasMatch(a);
      final isArabicB = RegExp(r'[\u0600-\u06FF]').hasMatch(b);

      if (isArabicA && !isArabicB) return -1; // Arabic comes first
      if (!isArabicA && isArabicB) return 1;  // English comes after
      return a.compareTo(b); // Same script, sort alphabetically
    });

    return sortedLetters;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _getContactsCountText() {
    try {
      final localizations = AppLocalizations.of(context);
      if (localizations != null) {
        return localizations.contactsToCategorizePlural(_uncategorizedContacts.length);
      }
      // Fallback text
      return '${_uncategorizedContacts.length} contacts to categorize';
    } catch (e) {
      // Safe fallback in case of any error
      return '${_uncategorizedContacts.length} contacts to categorize';
    }
  }

  Future<void> _loadData() async {
    try {
      if (mounted) {
        setState(() {
          _isLoading = true;
          _error = null;
        });
      }

      // Load saved position first so initialIndex is set correctly
      await _loadSavedPositionBeforeData();

      final user = SupabaseService.currentUser;
      if (user == null) return;

      // Load categories and existing contacts in parallel
      final results = await Future.wait([
        SupabaseService.getUserCategories(user.id),
        SupabaseService.getUserContacts(user.id),
      ]);

      final categories = results[0] as List<Category>;
      final categorizedContacts = results[1] as List<UserContact>;

      // Get uncategorized contacts
      final uncategorizedContacts =
          await ContactsService.getUncategorizedContacts(categorizedContacts);

      if (mounted) {
        // Validate and adjust current index if needed
        if (_currentIndex >= uncategorizedContacts.length) {
          _currentIndex = math.max(0, uncategorizedContacts.length - 1);
          await _saveCurrentPosition();
        }

        setState(() {
          _categories = categories;
          _uncategorizedContacts = uncategorizedContacts;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _assignContactToCategory(
      DeviceContact contact, Category category) async {
    try {
      final user = SupabaseService.currentUser;
      if (user == null) return;

      // Show loading dialog for multiple phone numbers
      if (contact.phoneNumbers.length > 1) {
        _showAssignmentProgressDialog(contact, category);
      }

      // Assign ALL phone numbers to the same category
      final List<String> successfulAssignments = [];
      final List<String> failedAssignments = [];

      for (final phoneNumber in contact.phoneNumbers) {
        final cleanedPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
        if (cleanedPhone.isNotEmpty) {
          try {
            await SupabaseService.assignContactToCategory(
              userId: user.id,
              contactPhone: cleanedPhone,
              categoryId: category.id,
            );
            successfulAssignments.add(cleanedPhone);
          } catch (e) {
            // Check if it's a duplicate key error (already assigned)
            final errorMessage = e.toString().toLowerCase();
            if (errorMessage.contains('duplicate key') || errorMessage.contains('unique constraint')) {
              // This phone number was already categorized, count as success
              successfulAssignments.add(cleanedPhone);
            } else {
              failedAssignments.add(cleanedPhone);
            }
          }
        }
      }

      // Close loading dialog if it was shown
      if (contact.phoneNumbers.length > 1 && mounted) {
        Navigator.of(context).pop();
      }

      // Handle results
      if (successfulAssignments.isNotEmpty) {
        // Mark contact as assigned and track the category
        final contactId = contact.phoneNumbers.isNotEmpty ? contact.phoneNumbers.first : contact.displayName;
        _assignedContactIds.add(contactId);
        _contactCategoryAssignments[contactId] = category.id;
        await _saveAssignedContacts();

        if (mounted) {
          // Add a small delay to make the transition smoother
          await Future.delayed(const Duration(milliseconds: 500));
          if (mounted) {
            // Move to next contact automatically (position is saved in _moveToNextContact)
            _moveToNextContact();
          }
        }

        // Success message removed - no more snackbar
      }

      // Show error for failed assignments
      if (failedAssignments.isNotEmpty && mounted) {
        await _showPartialAssignmentError(contact, category, successfulAssignments.length, failedAssignments.length);
      }

    } catch (e) {
      // Close loading dialog if it was shown
      if (contact.phoneNumbers.length > 1 && mounted) {
        Navigator.of(context).pop();
      }
      await _handleGeneralAssignmentError(e, contact, category);
    }
  }

  void _showAssignmentProgressDialog(DeviceContact contact, Category category) {
    if (!mounted) return;

    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          contentPadding: const EdgeInsets.all(24),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const CircularProgressIndicator(
                  strokeWidth: 3,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                AppLocalizations.of(context)?.assigningContact ?? 'Assigning Contact',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                AppLocalizations.of(context)?.assigningAllPhoneNumbers(
                  contact.phoneNumbers.length,
                  contact.displayName,
                  category.type.getDisplayName(context),
                ) ?? 'Assigning all ${contact.phoneNumbers.length} phone numbers for ${contact.displayName} to ${category.type.getDisplayName(context)}...',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _showPartialAssignmentError(
    DeviceContact contact,
    Category category,
    int successCount,
    int failedCount
  ) async {
    if (!mounted) return;

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          contentPadding: const EdgeInsets.all(24),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.warning_amber_rounded,
                  size: 40,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                AppLocalizations.of(context)?.partialAssignment ?? 'Partial Assignment',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                AppLocalizations.of(context)?.successfullyAssignedPhoneNumbers(
                  successCount,
                  contact.displayName,
                  failedCount,
                ) ?? 'Successfully assigned $successCount phone numbers for ${contact.displayName}, but $failedCount failed. The contact has been moved from the uncategorized list.',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                      child: Text(
                        AppLocalizations.of(context)?.ok ?? 'OK',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        _assignContactToCategory(contact, category);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Retry Failed',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _handleGeneralAssignmentError(dynamic error, DeviceContact contact, Category category) async {
    final errorMessage = error.toString().toLowerCase();

    if (errorMessage.contains('network') ||
        errorMessage.contains('connection') ||
        errorMessage.contains('clientexception') ||
        errorMessage.contains('socketexception') ||
        errorMessage.contains('failed host lookup') ||
        errorMessage.contains('internet')) {
      await showDialog(
        context: context,
        builder: (context) => NoInternetDialog(
          onRetry: () => _assignContactToCategory(contact, category),
        ),
      );
    } else if (errorMessage.contains('timeout')) {
      await _showErrorDialog(
        title: AppLocalizations.of(context)?.requestTimeout ?? 'Request Timeout',
        message: AppLocalizations.of(context)?.requestTookTooLong ?? 'The request took too long. Please try again.',
        icon: Icons.access_time,
        iconColor: Colors.orange,
        showRetry: true,
        onRetry: () => _assignContactToCategory(contact, category),
      );
    } else {
      await _showErrorDialog(
        title: AppLocalizations.of(context)?.assignmentFailed ?? 'Assignment Failed',
        message: AppLocalizations.of(context)?.failedToAssignContactToCategory(
          contact.displayName,
          category.type.getDisplayName(context),
        ) ?? 'Failed to assign ${contact.displayName} to ${category.type.getDisplayName(context)}. Please try again.',
        icon: Icons.error_outline,
        iconColor: Colors.red,
        showRetry: true,
        onRetry: () => _assignContactToCategory(contact, category),
      );
    }
  }

  Future<void> _showErrorDialog({
    required String title,
    required String message,
    required IconData icon,
    required Color iconColor,
    bool isWarning = false,
    bool showRetry = false,
    VoidCallback? onRetry,
  }) async {
    if (!mounted) return;

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          contentPadding: const EdgeInsets.all(24),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 40,
                  color: iconColor,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                message,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                      child: Text(
                        isWarning
                          ? (AppLocalizations.of(context)?.ok ?? 'OK')
                          : (AppLocalizations.of(context)?.cancel ?? 'Cancel'),
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ),
                  if (showRetry && onRetry != null) ...[
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          onRetry();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: iconColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)?.retry ?? 'Retry',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          body: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF667eea),
                  Color(0xFF764ba2),
                ],
              ),
            ),
            child: SafeArea(
              child: Column(
                children: [
                  _buildHeader(context),
                  Expanded(child: _buildBody()),
                ],
              ),
            ),
          ),
        ),
        // Alphabet Dropdown Overlay
        if (_showAlphabetDropdown) ...[
          // Backdrop to close dropdown
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _showAlphabetDropdown = false;
                });
              },
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
          // Dropdown content
          if (_showAlphabetDropdown)
            Positioned(
              top: 140, // Position below the header
              right: MediaQuery.of(context).size.width < 360 ? 8 : 24,
              left: MediaQuery.of(context).size.width < 360 ? 8 : null,
              child: _buildAlphabetDropdown(),
            ),
        ],
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 800),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: const EdgeInsets.fromLTRB(24, 20, 24, 16),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context)?.distributeContacts ?? 'Distribute Contacts',
                          style: Theme.of(context)
                              .textTheme
                              .headlineMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                shadows: [
                                  Shadow(
                                    color: Colors.black.withOpacity(0.3),
                                    offset: const Offset(0, 2),
                                    blurRadius: 4,
                                  ),
                                ],
                              ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          AppLocalizations.of(context)?.organizeYourContactsIntoCategories ?? 'Organize your contacts into categories',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.white.withOpacity(0.9),
                                    shadows: [
                                      Shadow(
                                        color: Colors.black.withOpacity(0.2),
                                        offset: const Offset(0, 1),
                                        blurRadius: 2,
                                      ),
                                    ],
                                  ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.refresh_rounded,
                        color: Colors.purple[600],
                      ),
                      onPressed: _loadData,
                      tooltip: AppLocalizations.of(context)?.refreshContacts ?? 'Refresh contacts',
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Colors.purple[600]!),
                    strokeWidth: 3,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context)?.loadingContacts ?? 'Loading contacts...',
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    if (_error != null) {
      // Check if it's a network-related error
      final errorMessage = _error!.toLowerCase();
      final isNetworkError = errorMessage.contains('clientexception') ||
          errorMessage.contains('socketexception') ||
          errorMessage.contains('failed host lookup') ||
          errorMessage.contains('network') ||
          errorMessage.contains('connection') ||
          errorMessage.contains('internet');

      if (isNetworkError) {
        return NoInternetWidget(
          onRetry: _loadData,
        );
      } else {
        // Show regular error for non-network errors
        return Center(
          child: Container(
            margin: const EdgeInsets.all(24),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.9),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    Icons.error_outline_rounded,
                    size: 48,
                    color: Colors.red[400],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  AppLocalizations.of(context)?.oopsSomethingWentWrong ?? 'Oops! Something went wrong',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[800],
                      ),
                ),
                const SizedBox(height: 8),
                Text(
                  _error!,
                  style: TextStyle(color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: _loadData,
                  icon: const Icon(Icons.refresh_rounded),
                  label: Text(AppLocalizations.of(context)?.tryAgain ?? 'Try Again'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple[600],
                    foregroundColor: Colors.white,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }

    if (_uncategorizedContacts.isEmpty) {
      return Center(
        child: Container(
          margin: const EdgeInsets.all(24),
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.check_circle_outline_rounded,
                  size: 64,
                  color: Colors.green[500],
                ),
              ),
              const SizedBox(height: 24),
              Text(
                AppLocalizations.of(context)?.allDone ?? 'All Done! 🎉',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                AppLocalizations.of(context)?.allYourContactsHaveBeenCategorized ?? 'All your contacts have been categorized!',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.grey[700],
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                AppLocalizations.of(context)?.greatJobOrganizingYourContacts ?? 'Great job organizing your contacts.',
                style: TextStyle(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: [
        // Contact count indicator and A-Z navigation
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 2),
          child: Row(
            children: [
              // Contact count indicator
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.purple[100],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.people_outline_rounded,
                          size: 20,
                          color: Colors.purple[600],
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        _getContactsCountText(),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[800],
                            ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // A-Z Navigation Button
              _buildAlphabetNavigationButton(),
            ],
          ),
        ),

        // Card swiper
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 0.0), // No top spacing
            child: _uncategorizedContacts.isNotEmpty
                ? CardSwiper(
                    key: ValueKey(_uncategorizedContacts
                        .length), // Force rebuild when count changes
                    controller: _controller,
                    cardsCount: _uncategorizedContacts.length,
                    initialIndex: _currentIndex,
                    numberOfCardsDisplayed:
                        math.min(_uncategorizedContacts.length, 3),
                    onSwipe: (previousIndex, currentIndex, direction) {
                      _onCardSwiped(previousIndex, currentIndex);
                      return true;
                    },
                    cardBuilder: (context, index, horizontalThresholdPercentage,
                        verticalThresholdPercentage) {
                      if (index >= _uncategorizedContacts.length) {
                        return const SizedBox.shrink();
                      }
                      final contact = _uncategorizedContacts[index];
                      final contactId = contact.phoneNumbers.isNotEmpty ? contact.phoneNumbers.first : contact.displayName;
                      final isAssigned = _assignedContactIds.contains(contactId);
                      final assignedCategoryId = _contactCategoryAssignments[contactId];
                      return ModernContactCard(
                        contact: contact,
                        categories: _categories,
                        onCategorySelected: (category) =>
                            _assignContactToCategory(contact, category),
                        isAssigned: isAssigned,
                        assignedCategoryId: assignedCategoryId,
                      );
                    },
                  )
                : Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          size: 80,
                          color: Colors.green[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          AppLocalizations.of(context)?.allContactsCategorized ?? 'All contacts categorized!',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[700],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          AppLocalizations.of(context)?.greatJobOrganizingYourContacts ?? 'Great job organizing your contacts.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
          ),
        ),

        // Navigation controls
        if (_uncategorizedContacts.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Previous button
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withOpacity(0.9),
                        Colors.white.withOpacity(0.7),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(16),
                      onTap: _currentIndex > 0 ? _moveToPreviousContact : null,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.arrow_back_ios_rounded,
                              color: _currentIndex > 0
                                  ? const Color(0xFF667eea)
                                  : Colors.grey[400],
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              AppLocalizations.of(context)?.previous ?? 'Previous',
                              style: TextStyle(
                                color: _currentIndex > 0
                                    ? const Color(0xFF667eea)
                                    : Colors.grey[400],
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // Position indicator
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF667eea).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: const Color(0xFF667eea).withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    '${_currentIndex + 1} / ${_uncategorizedContacts.length}',
                    style: const TextStyle(
                      color: Color(0xFF667eea),
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ),

                // Next button
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withOpacity(0.9),
                        Colors.white.withOpacity(0.7),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(16),
                      onTap: _currentIndex < _uncategorizedContacts.length - 1
                          ? _moveToNextContact
                          : null,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              AppLocalizations.of(context)?.next ?? 'Next',
                              style: TextStyle(
                                color: _currentIndex < _uncategorizedContacts.length - 1
                                    ? const Color(0xFF667eea)
                                    : Colors.grey[400],
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              Icons.arrow_forward_ios_rounded,
                              color: _currentIndex < _uncategorizedContacts.length - 1
                                  ? const Color(0xFF667eea)
                                  : Colors.grey[400],
                              size: 20,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  // A-Z Navigation Button Widget
  Widget _buildAlphabetNavigationButton() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine if we're on a small screen
        final isSmallScreen = MediaQuery.of(context).size.width < 360;

        return GestureDetector(
          onTap: _toggleAlphabetDropdown,
          child: Container(
            constraints: BoxConstraints(
              minWidth: isSmallScreen ? 50 : 60,
              maxWidth: isSmallScreen ? 80 : 100,
            ),
            padding: EdgeInsets.symmetric(
              horizontal: isSmallScreen ? 8 : 12,
              vertical: isSmallScreen ? 8 : 10,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.purple[400]!,
                  Colors.purple[600]!,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.purple.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.sort_by_alpha_rounded,
                  color: Colors.white,
                  size: isSmallScreen ? 16 : 18,
                ),
                if (!isSmallScreen) ...[
                  const SizedBox(width: 6),
                  Flexible(
                    child: Text(
                      AppLocalizations.of(context)?.azNavigation ?? 'أ-Z',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: isSmallScreen ? 11 : 12,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ],
                SizedBox(width: isSmallScreen ? 2 : 4),
                Icon(
                  _showAlphabetDropdown
                      ? Icons.keyboard_arrow_up_rounded
                      : Icons.keyboard_arrow_down_rounded,
                  color: Colors.white,
                  size: isSmallScreen ? 14 : 16,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // A-Z Dropdown Widget
  Widget _buildAlphabetDropdown() {
    final availableLetters = _getAvailableLetters();

    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        final isSmallScreen = screenWidth < 360;
        final dropdownWidth = isSmallScreen ? screenWidth * 0.9 : 280.0;
        final maxHeight = isSmallScreen ? 250.0 : 320.0;

        return Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            width: dropdownWidth,
            constraints: BoxConstraints(
              maxHeight: maxHeight,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Container(
                  padding: EdgeInsets.all(isSmallScreen ? 10 : 12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.purple[50]!,
                        Colors.purple[100]!,
                      ],
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.search_rounded,
                        color: Colors.purple[600],
                        size: isSmallScreen ? 16 : 18,
                      ),
                      const SizedBox(width: 6),
                      Flexible(
                        child: Text(
                          AppLocalizations.of(context)?.jumpToLetter ?? 'Jump to Letter',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.purple[800],
                            fontSize: isSmallScreen ? 12 : 14,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),

                // Letters Grid
                if (availableLetters.isNotEmpty)
                  Flexible(
                    child: Container(
                      padding: EdgeInsets.all(isSmallScreen ? 8 : 12),
                      child: SingleChildScrollView(
                        child: Wrap(
                          spacing: isSmallScreen ? 4 : 6,
                          runSpacing: isSmallScreen ? 4 : 6,
                          children: availableLetters.map((letter) {
                            final isSelected = _selectedLetter == letter;
                            final buttonSize = isSmallScreen ? 32.0 : 38.0;

                            return GestureDetector(
                              onTap: () => _jumpToLetter(letter),
                              child: Container(
                                width: buttonSize,
                                height: buttonSize,
                                decoration: BoxDecoration(
                                  gradient: isSelected
                                      ? LinearGradient(
                                          colors: [
                                            Colors.purple[400]!,
                                            Colors.purple[600]!,
                                          ],
                                        )
                                      : null,
                                  color: isSelected ? null : Colors.grey[100],
                                  borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                                  border: Border.all(
                                    color: isSelected
                                        ? Colors.purple[600]!
                                        : Colors.grey[300]!,
                                    width: 1,
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    letter,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: isSelected ? Colors.white : Colors.grey[700],
                                      fontSize: isSmallScreen ? 12 : 14,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  )
                else
                  Padding(
                    padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                    child: Text(
                      AppLocalizations.of(context)?.noContactsAvailable ?? 'No contacts available',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: isSmallScreen ? 12 : 14,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class ModernContactCard extends StatefulWidget {
  final DeviceContact contact;
  final List<Category> categories;
  final Function(Category) onCategorySelected;
  final bool isAssigned;
  final String? assignedCategoryId;

  const ModernContactCard({
    super.key,
    required this.contact,
    required this.categories,
    required this.onCategorySelected,
    this.isAssigned = false,
    this.assignedCategoryId,
  });

  @override
  State<ModernContactCard> createState() => _ModernContactCardState();
}

class _ModernContactCardState extends State<ModernContactCard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  bool _isDescriptionsExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        Colors.purple.shade50,
                        Colors.blue.shade50,
                      ],
                    ),
                  ),
                  child: Column(
                    children: [
                      // Removed "Already Assigned" banner - now showing indicator on category button instead

                      // Contact info section (upper 40%)
                      Expanded(
                        flex: 40,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Avatar
                              Hero(
                                tag: 'contact_${widget.contact.primaryPhone}',
                                child: Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.purple.withOpacity(0.3),
                                        blurRadius: 10,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                  child: CircleAvatar(
                                    radius: 35, // Reduced from 40 to 35
                                    backgroundColor: Colors.purple[200],
                                    child: widget.contact.avatar != null
                                        ? ClipOval(
                                            child: _buildSafeAvatar(),
                                          )
                                        : Text(
                                            widget.contact.displayName
                                                    .isNotEmpty
                                                ? widget.contact.displayName[0]
                                                    .toUpperCase()
                                                : '?',
                                            style: const TextStyle(
                                              fontSize: 24, // Reduced from 28 to 24
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white,
                                            ),
                                          ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 8), // Reduced from 10 to 8

                              // Name
                              Container(
                                constraints: const BoxConstraints(
                                  minHeight: 35, // Reduced from 50 to 35
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  widget.contact.displayName,
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineSmall
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.grey[800],
                                        fontSize: 16, // Reduced from 16 to 14
                                        height: 1.2, // Reduced line height
                                      ),
                                  textAlign: TextAlign.center,
                                  maxLines: 2, // Reduced from 3 to 2 lines
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),

                              // Phone number indicator
                              const SizedBox(height: 4), // Reduced from 8 to 4
                              // Container(
                              //   padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              //   decoration: BoxDecoration(
                              //     color: widget.contact.phoneNumbers.length > 1
                              //         ? Colors.blue.withOpacity(0.1)
                              //         : Colors.grey.withOpacity(0.1),
                              //     borderRadius: BorderRadius.circular(20),
                              //     border: Border.all(
                              //       color: widget.contact.phoneNumbers.length > 1
                              //           ? Colors.blue.withOpacity(0.3)
                              //           : Colors.grey.withOpacity(0.3),
                              //     ),
                              //   ),
                              //   child: Row(
                              //     mainAxisSize: MainAxisSize.min,
                              //     children: [
                              //       Icon(
                              //         widget.contact.phoneNumbers.length > 1
                              //             ? Icons.phone_android
                              //             : Icons.phone,
                              //         size: 16,
                              //         color: widget.contact.phoneNumbers.length > 1
                              //             ? Colors.blue[600]
                              //             : Colors.grey[600],
                              //       ),
                              //       const SizedBox(width: 4),
                              //       Text(
                              //         widget.contact.phoneNumbers.length > 1
                              //             ? '${widget.contact.phoneNumbers.length} numbers'
                              //             : '1 number',
                              //         style: TextStyle(
                              //           fontSize: 12,
                              //           fontWeight: FontWeight.w500,
                              //           color: widget.contact.phoneNumbers.length > 1
                              //               ? Colors.blue[600]
                              //               : Colors.grey[600],
                              //         ),
                              //       ),
                              //     ],
                              //   ),
                              // ),

                              // // Email (if available)
                              // if (widget.contact.email != null) ...[
                              //   const SizedBox(height: 6),
                              //   Flexible(
                              //     child: Text(
                              //       widget.contact.email!,
                              //       style: TextStyle(
                              //         fontSize: 13,
                              //         color: Colors.grey[600],
                              //       ),
                              //       textAlign: TextAlign.center,
                              //       maxLines: 1,
                              //       overflow: TextOverflow.ellipsis,
                              //     ),
                              //   ),
                              // ],
                            ],
                          ),
                        ),
                      ),

                      // Category tabs section (lower 60%)
                      Expanded(
                        flex: 60,
                        child: GestureDetector(
                          // Allow swipe gestures to pass through to the card swiper
                          behavior: HitTestBehavior.translucent,
                          child: _buildCategoryTabs(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCategoryTabs() {
    // Sort categories to put "preferAnytime" first and make it bigger
    final sortedCategories = List<Category>.from(widget.categories);
    sortedCategories.sort((a, b) {
      if (a.type == CategoryType.preferAnytime) return -1;
      if (b.type == CategoryType.preferAnytime) return 1;
      return 0;
    });

    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Category descriptions (foldable)
                      _buildFoldableCategoryDescriptions(),
                      const SizedBox(height: 8),

                      // Category buttons - Fixed height instead of Expanded to allow swipe gestures
                      SizedBox(
                        height: 220, // Reduced height to prevent overflow
                        child: GridView.count(
                          crossAxisCount: 2,
                          childAspectRatio: 2.0,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                          physics:
                              const NeverScrollableScrollPhysics(), // Disable grid scrolling to allow card swiping
                          children: sortedCategories.map((category) {
                            return _buildLargeCategoryButton(category);
                          }).toList(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLargeCategoryButton(Category category) {
    MaterialColor primaryColor;
    IconData icon;
    String description;
    final bool isAssigned = widget.assignedCategoryId == category.id;

    switch (category.type) {
      case CategoryType.preferAnytime:
        primaryColor = Colors.purple;
        icon = Icons.star;
        description = category.type.getDisplayName(context);
        break;
      case CategoryType.contactThroughMessages:
        primaryColor = Colors.blue;
        icon = Icons.message;
        description = category.type.getDisplayName(context);
        break;
      case CategoryType.contactAtTimes:
        primaryColor = Colors.orange;
        icon = Icons.schedule;
        description = category.type.getDisplayName(context);
        break;
      case CategoryType.contactAnytime:
        primaryColor = Colors.green;
        icon = Icons.access_time;
        description = category.type.getDisplayName(context);
        break;
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => widget.onCategorySelected(category),
        borderRadius: BorderRadius.circular(16),
        splashColor: Colors.white.withOpacity(0.3),
        highlightColor: Colors.white.withOpacity(0.1),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                primaryColor.shade400,
                primaryColor.shade600,
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: primaryColor.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      icon,
                      color: Colors.white,
                      size: 28,
                    ),
                    const SizedBox(height: 6),
                    Text(
                      description,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 13,
                        fontWeight: FontWeight.w700,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              // Assigned indicator
              if (isAssigned)
                Positioned(
                  top: 4,
                  right: 4,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.check,
                      color: primaryColor.shade600,
                      size: 16,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFoldableCategoryDescriptions() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade50, Colors.purple.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // Header - always visible
          GestureDetector(
            onTap: () {
              if (mounted) {
                setState(() {
                  _isDescriptionsExpanded = !_isDescriptionsExpanded;
                });
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      AppLocalizations.of(context)?.categoryDescriptions ?? 'Category Descriptions',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  AnimatedRotation(
                    turns: _isDescriptionsExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      size: 20,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Expandable content
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: _isDescriptionsExpanded
                ? 120
                : 0, // Reduced height to prevent overflow
            child: _isDescriptionsExpanded
                ? Container(
                    padding: const EdgeInsets.fromLTRB(12, 0, 12, 16),
                    child: Column(
                      children: [
                        const Divider(height: 1),
                        const SizedBox(height: 12),
                        // Scrollable content area
                        Expanded(
                          child: SingleChildScrollView(
                            physics: const BouncingScrollPhysics(),
                            child: Column(
                              children: [
                                _buildCompactCategoryDescription(
                                  '⭐',
                                  CategoryType.preferAnytime.getDisplayName(context),
                                  CategoryType.preferAnytime.getDefaultNote(context),
                                  Colors.purple,
                                ),
                                const SizedBox(height: 8),
                                _buildCompactCategoryDescription(
                                  '💬',
                                  CategoryType.contactThroughMessages.getDisplayName(context),
                                  CategoryType.contactThroughMessages.getDefaultNote(context),
                                  Colors.blue,
                                ),
                                const SizedBox(height: 8),
                                _buildCompactCategoryDescription(
                                  '⏰',
                                  CategoryType.contactAtTimes.getDisplayName(context),
                                  CategoryType.contactAtTimes.getDefaultNote(context),
                                  Colors.orange,
                                ),
                                const SizedBox(height: 8),
                                _buildCompactCategoryDescription(
                                  '🕐',
                                  CategoryType.contactAnytime.getDisplayName(context),
                                  CategoryType.contactAnytime.getDefaultNote(context),
                                  Colors.green,
                                ),
                                const SizedBox(
                                    height: 8), // Extra padding at bottom
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactCategoryDescription(
      String emoji, String title, String description, Color color) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: color.withOpacity(0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                emoji,
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color.withOpacity(0.9),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[700],
                    height: 1.4,
                  ),
                  softWrap: true,
                  overflow: TextOverflow.visible,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build avatar image safely to prevent UI freezing
  Widget _buildSafeAvatar() {
    final avatarData = widget.contact.avatar;
    if (avatarData == null || avatarData.isEmpty) {
      return _buildFallbackAvatar();
    }

    return FutureBuilder<Uint8List?>(
      future: _decodeAvatarSafely(avatarData),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          if (snapshot.hasData && snapshot.data != null) {
            return Image.memory(
              snapshot.data!,
              width: 70,
              height: 70,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                print('Error displaying avatar image: $error');
                return _buildFallbackAvatar();
              },
            );
          }
        }
        // Show fallback while loading or on error
        return _buildFallbackAvatar();
      },
    );
  }

  /// Build fallback avatar with initials
  Widget _buildFallbackAvatar() {
    return Text(
      widget.contact.displayName.isNotEmpty
          ? widget.contact.displayName[0].toUpperCase()
          : '?',
      style: const TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    );
  }

  /// Safely decode avatar data from various formats
  Future<Uint8List?> _decodeAvatarSafely(String avatarData) async {
    try {
      // Check if it's base64 encoded
      if (avatarData.startsWith('data:image/')) {
        // Remove data URL prefix if present
        final base64String = avatarData.split(',').last;
        return base64Decode(base64String);
      } else if (avatarData.length > 100 && !avatarData.contains('/')) {
        // Likely a base64 string without prefix
        return base64Decode(avatarData);
      } else {
        // Might be a file path or URL - not supported for now
        print('Unsupported avatar format: ${avatarData.substring(0, 50)}...');
        return null;
      }
    } catch (e) {
      print('Failed to decode avatar data: $e');
      return null;
    }
  }
}
