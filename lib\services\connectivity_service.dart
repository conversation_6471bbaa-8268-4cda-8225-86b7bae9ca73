import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

class ConnectivityService extends ChangeNotifier {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  bool _isOnline = false;
  bool get isOnline => _isOnline;
  
  bool _hasBeenOnline = false;
  bool get hasBeenOnline => _hasBeenOnline;

  final List<VoidCallback> _onlineCallbacks = [];
  final List<VoidCallback> _offlineCallbacks = [];

  Future<void> initialize() async {
    print('🌐 Initializing ConnectivityService...');
    
    // Check initial connectivity
    final connectivityResults = await _connectivity.checkConnectivity();
    _updateConnectionStatus(connectivityResults);
    
    // Listen for connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectionStatus,
      onError: (error) {
        print('❌ Connectivity error: $error');
      },
    );
    
    print('🌐 ConnectivityService initialized. Initial status: ${_isOnline ? 'Online' : 'Offline'}');
  }

  void _updateConnectionStatus(List<ConnectivityResult> connectivityResults) {
    final wasOnline = _isOnline;
    
    // Check if any connection type indicates we're online
    _isOnline = connectivityResults.any((result) => 
      result == ConnectivityResult.mobile ||
      result == ConnectivityResult.wifi ||
      result == ConnectivityResult.ethernet ||
      result == ConnectivityResult.vpn
    );
    
    if (_isOnline) {
      _hasBeenOnline = true;
    }
    
    print('🌐 Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
    print('🌐 Connection types: ${connectivityResults.map((e) => e.name).join(', ')}');
    
    // Notify listeners if status changed
    if (wasOnline != _isOnline) {
      notifyListeners();
      
      if (_isOnline) {
        print('🌐 Device came online - triggering online callbacks');
        for (final callback in _onlineCallbacks) {
          try {
            callback();
          } catch (e) {
            print('❌ Error in online callback: $e');
          }
        }
      } else {
        print('🌐 Device went offline - triggering offline callbacks');
        for (final callback in _offlineCallbacks) {
          try {
            callback();
          } catch (e) {
            print('❌ Error in offline callback: $e');
          }
        }
      }
    }
  }

  void addOnlineCallback(VoidCallback callback) {
    _onlineCallbacks.add(callback);
  }

  void addOfflineCallback(VoidCallback callback) {
    _offlineCallbacks.add(callback);
  }

  void removeOnlineCallback(VoidCallback callback) {
    _onlineCallbacks.remove(callback);
  }

  void removeOfflineCallback(VoidCallback callback) {
    _offlineCallbacks.remove(callback);
  }

  Future<bool> hasInternetConnection() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      return connectivityResults.any((result) => 
        result == ConnectivityResult.mobile ||
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.ethernet ||
        result == ConnectivityResult.vpn
      );
    } catch (e) {
      print('❌ Error checking internet connection: $e');
      return false;
    }
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    _onlineCallbacks.clear();
    _offlineCallbacks.clear();
    super.dispose();
  }
}
