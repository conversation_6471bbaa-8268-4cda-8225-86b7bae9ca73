import 'package:flutter/material.dart';

/// A widget that forces <PERSON><PERSON> (Left-to-Right) text direction for its child,
/// regardless of the app's current locale or RTL settings.
/// 
/// This is specifically used for time slots UI components to maintain
/// consistent left-to-right layout even when the app is in Arabic (RTL) mode.
class LTROverride extends StatelessWidget {
  final Widget child;
  
  const LTROverride({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: child,
    );
  }
}

/// A widget that forces LTR text direction specifically for time-related content
/// like time pickers, time displays, and time slot cards.
/// 
/// This ensures that time formats (like "9:00 AM - 5:00 PM") always display
/// in the expected left-to-right format regardless of app language.
class TimeDisplayLTR extends StatelessWidget {
  final Widget child;
  
  const TimeDisplayLTR({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: child,
    );
  }
}

/// A wrapper for time slot editing components that need to maintain
/// LTR layout for better usability and consistency.
class TimeSlotEditorLTR extends StatelessWidget {
  final Widget child;
  
  const TimeSlotEditorLTR({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: child,
    );
  }
}
