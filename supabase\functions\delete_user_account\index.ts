import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.0.0";

console.log("Delete user account function up and running");

serve(async (req) => {
  try {
    // Create instance of Supabase<PERSON>lient with SERVICE_ROLE key
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    );

    // Get the authorization header from the request.
    // When you invoke the function via the client library
    // it will automatically pass the authenticated user's JWT.
    const authHeader = req.headers.get("Authorization");

    if (!authHeader) {
      throw new Error("No authorization header found!");
    }

    // Get JWT from auth header
    const jwt = authHeader.replace("Bearer ", "");

    // Get the user object using the JWT
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser(jwt);

    if (userError || !user) {
      throw new Error(`No user found for JWT: ${userError?.message}`);
    }

    console.log(`🗑️ Starting account deletion for user: ${user.id}`);

    // Delete user data in order (due to foreign key constraints)

    // 1. Delete profile picture if exists
    try {
      const { data: files } = await supabaseClient.storage
        .from('profile-pictures')
        .list(user.id);

      if (files && files.length > 0) {
        const filePaths = files.map(file => `${user.id}/${file.name}`);
        await supabaseClient.storage
          .from('profile-pictures')
          .remove(filePaths);
        console.log('✅ Deleted profile pictures');
      }
    } catch (e) {
      console.log('⚠️ Warning: Could not delete profile pictures:', e);
    }

    // 2. Delete notification preferences
    try {
      await supabaseClient
        .from('notification_preferences')
        .delete()
        .or(`user_id.eq.${user.id},contact_user_id.eq.${user.id}`);
      console.log('✅ Deleted notification preferences');
    } catch (e) {
      console.log('⚠️ Warning: Could not delete notification preferences:', e);
    }

    // 3. Delete user contacts (both as categorizer and categorized)
    try {
      // Get user's phone number first
      const { data: userProfile } = await supabaseClient
        .from('profiles')
        .select('phone_number')
        .eq('id', user.id)
        .single();
      
      const userPhone = userProfile?.phone_number;
      
      if (userPhone) {
        // Delete contacts where user is the categorizer
        await supabaseClient
          .from('user_contacts')
          .delete()
          .eq('user_id', user.id);
        
        // Delete contacts where user is the categorized contact
        await supabaseClient
          .from('user_contacts')
          .delete()
          .eq('categorized_contact_phone', userPhone);
      }
      console.log('✅ Deleted user contacts');
    } catch (e) {
      console.log('⚠️ Warning: Could not delete user contacts:', e);
    }

    // 4. Get user's category IDs first, then delete time slots
    try {
      const { data: userCategories } = await supabaseClient
        .from('categories')
        .select('id')
        .eq('user_id', user.id);

      const categoryIds = userCategories?.map((cat: any) => cat.id) || [];

      if (categoryIds.length > 0) {
        await supabaseClient
          .from('time_slots')
          .delete()
          .in('category_id', categoryIds);
        console.log(`✅ Deleted time slots for ${categoryIds.length} categories`);
      }
    } catch (e) {
      console.log('⚠️ Warning: Could not delete time slots:', e);
    }

    // 5. Delete categories
    try {
      await supabaseClient
        .from('categories')
        .delete()
        .eq('user_id', user.id);
      console.log('✅ Deleted categories');
    } catch (e) {
      console.log('⚠️ Warning: Could not delete categories:', e);
    }

    // 6. Delete profile
    try {
      await supabaseClient
        .from('profiles')
        .delete()
        .eq('id', user.id);
      console.log('✅ Deleted profile');
    } catch (e) {
      console.log('⚠️ Warning: Could not delete profile:', e);
    }

    // 7. Delete auth user (this requires admin privileges)
    const { data, error } = await supabaseClient.auth.admin.deleteUser(user.id);

    if (error) {
      throw new Error(`Failed to delete auth user: ${error.message}`);
    }

    console.log('🗑️ Account deletion completed successfully');

    return new Response(JSON.stringify({ 
      success: true, 
      message: "Account deleted successfully",
      data 
    }), {
      headers: { "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    console.error('❌ Error deleting account:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: error.message || "Unknown error occurred" 
    }), {
      headers: { "Content-Type": "application/json" },
      status: 400,
    });
  }
});
