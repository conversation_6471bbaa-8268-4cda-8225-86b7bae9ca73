import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../models/models.dart';
import '../../models/category_extensions.dart';
import '../../services/supabase_service.dart';
import '../../services/offline_contact_service.dart';
import '../../services/connectivity_service.dart';
import '../../services/local_database_service.dart';
import '../../widgets/ltr_override.dart';
import 'time_slots_editor.dart';

class CategoriesScreen extends StatefulWidget {
  const CategoriesScreen({super.key});

  @override
  State<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends State<CategoriesScreen>
    with TickerProviderStateMixin {
  List<Category> _categories = [];
  bool _isLoading = false; // Start with false since we use cache-first approach
  String? _error;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Offline-first services
  final OfflineContactService _offlineService = OfflineContactService();
  final ConnectivityService _connectivityService = ConnectivityService();
  bool _isOnline = true;
  bool _hasPendingChanges = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Initialize connectivity monitoring
    _initializeConnectivity();
    _loadCategories();
  }

  void _initializeConnectivity() {
    // Set initial connectivity state
    _isOnline = _connectivityService.isOnline;

    // Listen to connectivity changes
    _connectivityService.addListener(_onConnectivityChanged);
  }

  void _onConnectivityChanged() {
    if (mounted) {
      setState(() {
        _isOnline = _connectivityService.isOnline;
      });

      // If we just came online, sync pending changes first, then reload
      if (_isOnline) {
        _handleConnectivityRestored();
      }
    }
  }

  Future<void> _handleConnectivityRestored() async {
    try {
      print('📱 Connectivity restored - waiting for global sync...');

      // Wait for the global SyncService to process pending operations
      // This prevents duplicate sync processing
      await Future.delayed(const Duration(seconds: 2));

      // Check if there are still pending changes after global sync
      final remainingOperations = await LocalDatabaseService.getPendingSyncOperations();
      final hasPendingCategoryChanges = remainingOperations.any(
        (op) => op['operation_type'] == 'UPDATE_CATEGORY'
      );

      if (hasPendingCategoryChanges) {
        print('📱 Global sync missed some category changes - processing manually...');
        await _syncPendingChanges();
      }

      // Update cache in background without reloading UI
      print('📱 Updating cache after sync...');
      _offlineService.refreshCategoriesCache();

      print('📱 Connectivity restoration complete');
    } catch (e) {
      print('❌ Error handling connectivity restoration: $e');
    }
  }

  @override
  void dispose() {
    _connectivityService.removeListener(_onConnectivityChanged);
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _updateOldEnglishCategoryNotes() async {
    try {
      final user = SupabaseService.currentUser;
      if (user == null || !mounted) return;

      print('📱 Checking for old English category notes to update...');

      // Get all categories for the current user
      final categories = await _offlineService.getUserCategories();

      bool hasUpdates = false;

      for (final category in categories) {
        final currentNote = category.note;

        // Check if current note is one of the old English defaults
        final isOldEnglishDefault = currentNote == 'You can contact me at any time.' ||
            currentNote == 'You can contact me at any time' ||
            currentNote == 'You can contact me at any time, but I\'d prefer if you use these times if the call can wait.' ||
            currentNote == 'Please contact me at these times.' ||
            currentNote == 'Please contact me through messages at the following times.' ||
            currentNote == 'Pleease contact me through messages at the following times..' || // Handle typo version
            currentNote.startsWith('You can contact me at any time') ||
            currentNote.startsWith('Please contact me at these times') ||
            currentNote.startsWith('Please contact me through messages') ||
            currentNote.startsWith('Pleease contact me through messages'); // Handle typo version

        if (isOldEnglishDefault && mounted) {
          // Get the localized default note for this category type
          final localizedNote = category.type.getDefaultNote(context);

          print('📱 Auto-updating category ${category.type.name} note: "$currentNote" → "$localizedNote"');

          // Update the category note using the existing method
          await _updateCategoryNote(category, localizedNote);
          hasUpdates = true;
        }
      }

      if (hasUpdates) {
        print('📱 Finished updating old English category notes');
        // Refresh the categories list to reflect the changes
        if (mounted) {
          final updatedCategories = await _offlineService.getUserCategories();
          setState(() {
            _categories = updatedCategories;
          });
        }
      } else {
        print('📱 No old English category notes found to update');
      }
    } catch (e) {
      print('❌ Error updating old English category notes: $e');
    }
  }

  Future<void> _loadCategories() async {
    try {
      final user = SupabaseService.currentUser;
      if (user == null) return;

      // Cache-first approach: Load immediately from cache without loading state
      print('📱 Categories Screen: Loading from cache (cache-first approach)');
      final categories = await _offlineService.getUserCategories();

      print('📱 Categories Screen: Loaded ${categories.length} categories from cache');
      for (final category in categories) {
        print('📱   - ${category.type.name} (${category.timeSlots.length} slots)');
      }

      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoading = false;
          _error = null;
        });
        _animationController.forward();
      }

      // Background tasks (don't affect UI loading)
      _performBackgroundTasks();
    } catch (e) {
      print('❌ Error loading categories: $e');
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  // Perform background tasks without affecting UI loading
  Future<void> _performBackgroundTasks() async {
    try {
      // Check and update pending changes status
      await _updatePendingChangesStatus();

      // Update any old English category notes to localized versions
      await _updateOldEnglishCategoryNotes();

      // Background sync if online (don't reload UI)
      if (_isOnline) {
        _syncInBackground();
      }
    } catch (e) {
      print('❌ Error in background tasks: $e');
    }
  }

  // Background sync without UI reloads
  Future<void> _syncInBackground() async {
    try {
      print('📱 Performing background sync...');
      await _offlineService.refreshCategoriesCache();

      // Only update UI if data actually changed
      final newCategories = await _offlineService.getUserCategories();
      if (mounted && _categoriesChanged(newCategories)) {
        print('📱 Categories changed during background sync, updating UI');
        setState(() {
          _categories = newCategories;
        });
      }
    } catch (e) {
      print('❌ Background sync failed: $e');
    }
  }

  // Check if categories actually changed to avoid unnecessary UI updates
  bool _categoriesChanged(List<Category> newCategories) {
    if (_categories.length != newCategories.length) return true;

    for (int i = 0; i < _categories.length; i++) {
      final old = _categories[i];
      final new_ = newCategories[i];
      if (old.id != new_.id ||
          old.note != new_.note ||
          old.timeSlots.length != new_.timeSlots.length) {
        return true;
      }
    }
    return false;
  }

  Future<void> _updatePendingChangesStatus() async {
    try {
      // Check if we have any pending sync operations
      final pendingOperations = await LocalDatabaseService.getPendingSyncOperations();
      final hadPendingChanges = _hasPendingChanges;
      _hasPendingChanges = pendingOperations.isNotEmpty;

      // If pending status changed, update UI
      if (hadPendingChanges != _hasPendingChanges && mounted) {
        setState(() {});

        if (!_hasPendingChanges && hadPendingChanges) {
          print('📱 All pending changes have been synced');
        }
      }
    } catch (e) {
      print('❌ Error checking pending changes status: $e');
    }
  }

  Future<void> _updateCategoryNote(Category category, String newNote) async {
    try {
      final updatedCategory = category.copyWith(note: newNote);

      // Update local state immediately for responsive UI
      setState(() {
        final index = _categories.indexWhere((c) => c.id == category.id);
        if (index != -1) {
          _categories[index] = updatedCategory;
        }
      });

      // Update local database immediately
      await LocalDatabaseService.insertCategory(updatedCategory);

      if (_isOnline) {
        // Try to sync to server if online
        try {
          await SupabaseService.updateCategory(updatedCategory);

          // Clear pending changes flag since sync was successful
          _hasPendingChanges = false;

          // Update cache in background without reloading UI (UI already updated above)
          await _offlineService.refreshCategoriesCache();

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(AppLocalizations.of(context)?.categoryNoteUpdatedSuccessfully ?? 'Category note updated successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } catch (e) {
          // If server update fails, queue for later sync
          await _queueCategoryUpdate(updatedCategory);
          _hasPendingChanges = true;

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(AppLocalizations.of(context)?.updatedLocally ?? 'Updated locally. Will sync when online.'),
                backgroundColor: Colors.orange,
                action: SnackBarAction(
                  label: AppLocalizations.of(context)?.retry ?? 'Retry',
                  onPressed: () => _updateCategoryNote(category, newNote),
                ),
              ),
            );
          }
        }
      } else {
        // Offline mode - queue for later sync
        await _queueCategoryUpdate(updatedCategory);
        _hasPendingChanges = true;

        // Update cache to reflect local changes
        await _offlineService.refreshCategoriesCache();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)?.updatedOffline ?? 'Updated offline. Will sync when online.'),
              backgroundColor: Colors.blue,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)?.failedToUpdateCategory ?? 'Failed to update category: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateCategoryWithTimeSlots(Category category, List<TimeSlot> timeSlots) async {
    final updatedCategory = category.copyWith(timeSlots: timeSlots);
    await _updateCategoryNote(updatedCategory, updatedCategory.note);
  }

  void _editTimeSlots(Category category) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TimeSlotsEditor(
          category: category,
          onSaved: (timeSlots) async {
            // Update the category with new time slots
            final updatedCategory = category.copyWith(timeSlots: timeSlots);

            // Update local state immediately for responsive UI
            setState(() {
              final index = _categories.indexWhere((c) => c.id == category.id);
              if (index != -1) {
                _categories[index] = updatedCategory;
              }
            });

            // Update local database immediately
            await LocalDatabaseService.insertCategory(updatedCategory);

            if (_isOnline) {
              // Try to sync to server if online
              try {
                print('🔄 Syncing category and time slots to server...');

                // Update category first
                await SupabaseService.updateCategory(updatedCategory);
                print('🔄 ✅ Category updated on server');

                // Update time slots separately
                await SupabaseService.updateTimeSlots(updatedCategory.id, updatedCategory.timeSlots);
                print('🔄 ✅ Time slots updated on server');

                // Clear pending changes flag since sync was successful
                _hasPendingChanges = false;

                // Update cache in background without reloading UI (UI already updated above)
                print('🔄 Refreshing cache after successful sync...');
                await _offlineService.refreshCategoriesCache();

                if (mounted && context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(AppLocalizations.of(context)?.timeSlotsUpdatedSuccessfully ?? 'Time slots updated successfully'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                print('🔄 ❌ Server sync failed: $e');
                // If server update fails, queue for later sync
                await _queueCategoryUpdate(updatedCategory);
                _hasPendingChanges = true;

                if (mounted && context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(AppLocalizations.of(context)?.updatedLocally ?? 'Updated locally. Will sync when online.'),
                      backgroundColor: Colors.orange,
                      action: SnackBarAction(
                        label: AppLocalizations.of(context)?.retry ?? 'Retry',
                        onPressed: () => _updateCategoryWithTimeSlots(category, timeSlots),
                      ),
                    ),
                  );
                }
              }
            } else {
              print('🔄 Offline mode - queuing for later sync');
              // Offline mode - queue for later sync
              await _queueCategoryUpdate(updatedCategory);
              _hasPendingChanges = true;

              // Update cache to reflect local changes
              await _offlineService.refreshCategoriesCache();

              if (mounted && context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(AppLocalizations.of(context)?.updatedOffline ?? 'Updated offline. Will sync when online.'),
                    backgroundColor: Colors.blue,
                    duration: const Duration(seconds: 2),
                  ),
                );
              }
            }
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context)?.myCategories ?? 'My Categories',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 24,
            color: Colors.white,
          ),
        ),
        automaticallyImplyLeading: false,
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF667eea),
                Color(0xFF764ba2),
              ],
            ),
          ),
        ),
        actions: [
          // Offline/Sync status indicator
          if (!_isOnline || _hasPendingChanges) ...[
            Container(
              margin: const EdgeInsets.only(right: 8),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _isOnline
                    ? Colors.orange.withOpacity(0.9)
                    : Colors.red.withOpacity(0.9),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _isOnline ? Icons.sync_problem : Icons.wifi_off,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _isOnline ? (AppLocalizations.of(context)?.pending ?? 'Pending') : (AppLocalizations.of(context)?.offline ?? 'Offline'),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
          Container(
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: _loadCategories,
            ),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
          ),
        ),
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF667eea)),
                strokeWidth: 3,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              AppLocalizations.of(context)?.loadingYourCategories ?? 'Loading your categories...',
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Container(
          margin: const EdgeInsets.all(24),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.95),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(50),
                ),
                child: Icon(
                  Icons.error_outline,
                  size: 48,
                  color: Colors.red[400],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context)?.oopsSomethingWentWrong ?? 'Oops! Something went wrong',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _loadCategories,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF667eea),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  AppLocalizations.of(context)?.tryAgain ?? 'Try Again',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        margin: const EdgeInsets.only(top: 100), // Account for app bar
        child: ListView.builder(
          padding: const EdgeInsets.all(20),
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            return AnimatedContainer(
              duration: Duration(milliseconds: 300 + (index * 100)),
              curve: Curves.easeOutBack,
              child: ModernCategoryCard(
                category: category,
                index: index,
                onEditNote: (newNote) => _updateCategoryNote(category, newNote),
                onEditTimeSlots: () => _editTimeSlots(category),
              ),
            );
          },
        ),
      ),
    );
  }

  // Helper methods for offline functionality
  Future<void> _queueCategoryUpdate(Category category) async {
    try {
      await LocalDatabaseService.addToSyncQueue(
        operationType: 'UPDATE_CATEGORY',
        tableName: 'categories',
        recordId: category.id,
        data: category.toJsonWithTimeSlots(),
      );
      print('📱 Queued category update for sync: ${category.id}');
      print('📱 Queued data includes ${category.timeSlots.length} time slots');
    } catch (e) {
      print('❌ Failed to queue category update: $e');
    }
  }

  Future<void> _syncPendingChanges() async {
    if (!_isOnline) return;

    try {
      print('📱 Syncing pending changes...');

      // Get pending sync operations
      final pendingOperations = await LocalDatabaseService.getPendingSyncOperations();

      for (final operation in pendingOperations) {
        try {
          if (operation['operation_type'] == 'UPDATE_CATEGORY') {
            final categoryDataString = operation['data'] as String;
            final categoryData = jsonDecode(categoryDataString) as Map<String, dynamic>;
            final category = Category.fromJson(categoryData);

            print('📱 Syncing category from queue: ${category.id} (${category.type.name})');
            print('📱 Category has ${category.timeSlots.length} time slots in queue data');

            // Try to sync category to server
            await SupabaseService.updateCategory(category);
            print('📱 ✅ Category synced to server');

            // If category has time slots, sync them separately
            if (category.timeSlots.isNotEmpty) {
              print('📱 Syncing ${category.timeSlots.length} time slots to server...');
              await SupabaseService.updateTimeSlots(category.id, category.timeSlots);
              print('📱 ✅ Time slots synced to server');
            }

            // Remove from sync queue on success
            await LocalDatabaseService.removeSyncOperation(operation['id']);
            print('📱 ✅ Removed operation from sync queue: ${category.id}');
          }
        } catch (e) {
          print('❌ Failed to sync operation ${operation['id']}: $e');
          // Keep in queue for next attempt
        }
      }

      // Check if we still have pending changes
      final remainingOperations = await LocalDatabaseService.getPendingSyncOperations();
      final hadPendingChanges = _hasPendingChanges;
      _hasPendingChanges = remainingOperations.isNotEmpty;

      // If we successfully synced some changes, update the UI state
      if (hadPendingChanges && !_hasPendingChanges) {
        // Update cache in background without reloading UI
        print('📱 All changes synced - updating cache...');
        _offlineService.refreshCategoriesCache();

        if (mounted) {
          setState(() {}); // Update UI to reflect sync status
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)?.allChangesSyncedSuccessfully ?? 'All changes synced successfully'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      print('❌ Error syncing pending changes: $e');
    }
  }
}

class ModernCategoryCard extends StatefulWidget {
  final Category category;
  final int index;
  final Function(String) onEditNote;
  final VoidCallback onEditTimeSlots;

  const ModernCategoryCard({
    super.key,
    required this.category,
    required this.index,
    required this.onEditNote,
    required this.onEditTimeSlots,
  });

  @override
  State<ModernCategoryCard> createState() => _ModernCategoryCardState();
}

class _ModernCategoryCardState extends State<ModernCategoryCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _pulseController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Start pulsing animation if there are active time slots
    _checkForActiveSlots();
  }

  void _checkForActiveSlots() {
    // Get current day of week (0 = Sunday, 1 = Monday, etc.)
    final currentDayOfWeek = DateTime.now().weekday % 7; // Convert to 0-6 format

    // Check only today's time slots for active status
    final todaysTimeSlots = widget.category.timeSlots
        .where((slot) => slot.dayOfWeek == currentDayOfWeek)
        .toList();

    final hasActiveSlots = todaysTimeSlots.any((slot) => slot.isCurrentTimeInSlot());
    if (hasActiveSlots) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categoryColor = _getCategoryColor(widget.category.type);

    // Get current day of week (0 = Sunday, 1 = Monday, etc.)
    final currentDayOfWeek = DateTime.now().weekday % 7; // Convert to 0-6 format

    // Filter time slots for current day only
    final todaysTimeSlots = widget.category.timeSlots
        .where((slot) => slot.dayOfWeek == currentDayOfWeek)
        .toList();

    final hasActiveSlots = todaysTimeSlots.any((slot) => slot.isCurrentTimeInSlot());

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) {
              _hoverController.forward();
              setState(() => _isHovered = true);
            },
            onTapUp: (_) {
              _hoverController.reverse();
              setState(() => _isHovered = false);
            },
            onTapCancel: () {
              _hoverController.reverse();
              setState(() => _isHovered = false);
            },
            child: Container(
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white,
                    Colors.white.withOpacity(0.95),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: categoryColor.withOpacity(0.3),
                    blurRadius: _isHovered ? 25 : 15,
                    offset: Offset(0, _isHovered ? 12 : 8),
                    spreadRadius: _isHovered ? 2 : 0,
                  ),
                  BoxShadow(
                    color: Colors.white.withOpacity(0.8),
                    blurRadius: 10,
                    offset: const Offset(-5, -5),
                  ),
                  // Add special glow for active time slots
                  if (hasActiveSlots) ...[
                    BoxShadow(
                      color: Colors.green.withOpacity(0.4),
                      blurRadius: 20,
                      offset: const Offset(0, 0),
                      spreadRadius: 3,
                    ),
                    BoxShadow(
                      color: Colors.green.withOpacity(0.2),
                      blurRadius: 30,
                      offset: const Offset(0, 0),
                      spreadRadius: 5,
                    ),
                  ],
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Active indicator bar at the top
                    if (hasActiveSlots)
                      Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        height: 4,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.green[400] ?? const Color(0xFF66BB6A),
                              Colors.green[600] ?? const Color(0xFF43A047),
                              Colors.green[400] ?? const Color(0xFF66BB6A),
                            ],
                            stops: const [0.0, 0.5, 1.0],
                          ),
                          borderRadius: BorderRadius.circular(2),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.green.withOpacity(0.5),
                              blurRadius: 4,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                      ),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                categoryColor,
                                categoryColor.withOpacity(0.8),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: categoryColor.withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Icon(
                            _getCategoryIcon(widget.category.type),
                            size: 28,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.category.type.getDisplayName(context),
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w700,
                                  color: Colors.grey[800],
                                  letterSpacing: -0.5,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Container(
                                height: 3,
                                width: 40,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [categoryColor, categoryColor.withOpacity(0.5)],
                                  ),
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: categoryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            icon: Icon(
                              Icons.edit_rounded,
                              color: categoryColor,
                              size: 20,
                            ),
                            onPressed: () => _showEditNoteDialog(context),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.grey[200]!,
                          width: 1,
                        ),
                      ),
                      child: Text(
                        widget.category.note,
                        style: TextStyle(
                          fontSize: 15,
                          color: Colors.grey[700],
                          fontStyle: FontStyle.normal,
                          height: 1.4,
                        ),
                      ),
                    ),
                    if (widget.category.hasTimeSlots) ...[
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              categoryColor.withOpacity(0.1),
                              categoryColor.withOpacity(0.05),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: categoryColor.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.schedule_rounded,
                                  size: 20,
                                  color: categoryColor,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  todaysTimeSlots.isNotEmpty
                                      ? (AppLocalizations.of(context)?.todaysTimeSlots(todaysTimeSlots.length) ?? 'Today\'s Time Slots (${todaysTimeSlots.length})')
                                      : (AppLocalizations.of(context)?.timeSlotsCount(widget.category.timeSlots.length) ?? 'Time Slots (${widget.category.timeSlots.length})'),
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: categoryColor,
                                  ),
                                ),
                                const Spacer(),
                                Container(
                                  decoration: BoxDecoration(
                                    color: categoryColor,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: TextButton(
                                    onPressed: widget.onEditTimeSlots,
                                    style: TextButton.styleFrom(
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 8,
                                      ),
                                    ),
                                    child: Text(
                                      AppLocalizations.of(context)?.edit ?? 'Edit',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            if (todaysTimeSlots.isNotEmpty) ...[
                              const SizedBox(height: 12),
                              ...todaysTimeSlots.take(3).map((slot) {
                                final isActive = slot.isCurrentTimeInSlot();
                                return AnimatedBuilder(
                                  animation: _pulseAnimation,
                                  builder: (context, child) {
                                    return Transform.scale(
                                      scale: isActive ? _pulseAnimation.value : 1.0,
                                      child: Container(
                                  margin: const EdgeInsets.only(bottom: 8),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 10,
                                  ),
                                  decoration: BoxDecoration(
                                    gradient: isActive
                                        ? LinearGradient(
                                            colors: [
                                              Colors.green[50] ?? const Color(0xFFE8F5E8),
                                              Colors.green[100] ?? const Color(0xFFC8E6C9),
                                            ],
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight,
                                          )
                                        : null,
                                    color: isActive ? null : Colors.white,
                                    borderRadius: BorderRadius.circular(10),
                                    border: Border.all(
                                      color: isActive
                                          ? Colors.green[300] ?? const Color(0xFFA5D6A7)
                                          : Colors.grey[200] ?? const Color(0xFFEEEEEE),
                                      width: isActive ? 2 : 1,
                                    ),
                                    boxShadow: isActive ? [
                                      BoxShadow(
                                        color: Colors.green.withOpacity(0.2),
                                        blurRadius: 8,
                                        offset: const Offset(0, 3),
                                        spreadRadius: 1,
                                      ),
                                    ] : null,
                                  ),
                                  child: Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(6),
                                        decoration: BoxDecoration(
                                          color: isActive
                                              ? Colors.green[100] ?? const Color(0xFFC8E6C9)
                                              : Colors.grey[100] ?? const Color(0xFFF5F5F5),
                                          borderRadius: BorderRadius.circular(6),
                                        ),
                                        child: Icon(
                                          isActive
                                              ? Icons.radio_button_checked_rounded
                                              : Icons.access_time_rounded,
                                          size: 16,
                                          color: isActive
                                              ? Colors.green[700] ?? const Color(0xFF388E3C)
                                              : Colors.grey[600] ?? const Color(0xFF757575),
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            // Day and time display
                                            TimeDisplayLTR(
                                              child: Text(
                                                '${slot.getDayName(context)}: ${slot.timeRange}',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: isActive
                                                    ? Colors.green[800] ?? const Color(0xFF2E7D32)
                                                    : Colors.grey[700] ?? const Color(0xFF616161),
                                                fontWeight: isActive
                                                    ? FontWeight.w600
                                                    : FontWeight.w500,
                                              ),
                                              ),
                                            ),
                                            // Active status indicator
                                            if (isActive) ...[
                                              const SizedBox(height: 6),
                                              Row(
                                                children: [
                                                  Container(
                                                    padding: const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 3,
                                                    ),
                                                    decoration: BoxDecoration(
                                                      gradient: LinearGradient(
                                                        colors: [
                                                          Colors.green[600] ?? const Color(0xFF43A047),
                                                          Colors.green[500] ?? const Color(0xFF4CAF50),
                                                        ],
                                                      ),
                                                      borderRadius: BorderRadius.circular(10),
                                                      boxShadow: [
                                                        BoxShadow(
                                                          color: Colors.green.withOpacity(0.3),
                                                          blurRadius: 4,
                                                          offset: const Offset(0, 2),
                                                        ),
                                                      ],
                                                    ),
                                                    child: Text(
                                                      AppLocalizations.of(context)?.activeNowLabel ?? 'ACTIVE NOW',
                                                      style: const TextStyle(
                                                        fontSize: 9,
                                                        fontWeight: FontWeight.bold,
                                                        color: Colors.white,
                                                        letterSpacing: 0.5,
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Container(
                                                    width: 6,
                                                    height: 6,
                                                    decoration: BoxDecoration(
                                                      color: Colors.green[600] ?? const Color(0xFF43A047),
                                                      shape: BoxShape.circle,
                                                      boxShadow: [
                                                        BoxShadow(
                                                          color: Colors.green.withOpacity(0.5),
                                                          blurRadius: 4,
                                                          spreadRadius: 1,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  const SizedBox(width: 6),
                                                  Expanded(
                                                    child: Text(
                                                      AppLocalizations.of(context)?.perfectTimeToContact ?? 'Perfect time to contact!',
                                                      style: TextStyle(
                                                        fontSize: 11,
                                                        color: Colors.green[700] ?? const Color(0xFF388E3C),
                                                        fontWeight: FontWeight.w500,
                                                        fontStyle: FontStyle.italic,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                      ),
                                    );
                                  },
                                );
                              }),
                              if (todaysTimeSlots.length > 3)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                  child: Text(
                                    AppLocalizations.of(context)?.andMoreSlotsToday(todaysTimeSlots.length - 3) ?? '... and ${todaysTimeSlots.length - 3} more slots today',
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: Colors.grey[500] ?? const Color(0xFF9E9E9E),
                                      fontStyle: FontStyle.italic,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                            ] else if (widget.category.timeSlots.isNotEmpty) ...[
                              // Show message when there are time slots but none for today
                              const SizedBox(height: 12),
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.blue[50],
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.blue[200]!,
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.info_outline,
                                      size: 16,
                                      color: Colors.blue[600],
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        AppLocalizations.of(context)?.noTimeSlotsForToday(widget.category.timeSlots.length, widget.category.timeSlots.length != 1 ? 's' : '') ?? 'No time slots for today. You have ${widget.category.timeSlots.length} slot${widget.category.timeSlots.length != 1 ? 's' : ''} on other days.',
                                        style: TextStyle(
                                          fontSize: 13,
                                          color: Colors.blue[700],
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ] else
                              Container(
                                margin: const EdgeInsets.only(top: 12),
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.grey[100] ?? const Color(0xFFF5F5F5),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.info_outline,
                                      size: 16,
                                      color: Colors.grey[400] ?? const Color(0xFFBDBDBD),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      AppLocalizations.of(context)?.noTimeSlotsConfiguredYet ?? 'No time slots configured yet',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[500] ?? const Color(0xFF9E9E9E),
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getCategoryColor(CategoryType type) {
    switch (type) {
      case CategoryType.contactAnytime:
        return const Color(0xFF4CAF50); // Green
      case CategoryType.preferAnytime:
        return const Color(0xFF2196F3); // Blue
      case CategoryType.contactAtTimes:
        return const Color(0xFFFF9800); // Orange
      case CategoryType.contactThroughMessages:
        return const Color(0xFF9C27B0); // Purple
    }
  }

  IconData _getCategoryIcon(CategoryType type) {
    switch (type) {
      case CategoryType.contactAnytime:
        return Icons.access_time_rounded;
      case CategoryType.preferAnytime:
        return Icons.schedule_rounded;
      case CategoryType.contactAtTimes:
        return Icons.alarm_rounded;
      case CategoryType.contactThroughMessages:
        return Icons.message_rounded;
    }
  }

  void _showEditNoteDialog(BuildContext context) {
    // Use localized default note if the current note is the old English default
    final defaultNote = widget.category.type.getDefaultNote(context);
    final currentNote = widget.category.note;

    // Check if current note is one of the old English defaults
    final isOldEnglishDefault = currentNote == 'You can contact me at any time.' ||
        currentNote == 'You can contact me at any time' ||
        currentNote == 'You can contact me at any time, but I\'d prefer if you use these times if the call can wait.' ||
        currentNote == 'Please contact me at these times.' ||
        currentNote == 'Please contact me through messages at the following times.' ||
        currentNote == 'Pleease contact me through messages at the following times..' || // Handle typo version
        currentNote.startsWith('You can contact me at any time') ||
        currentNote.startsWith('Please contact me at these times') ||
        currentNote.startsWith('Please contact me through messages') ||
        currentNote.startsWith('Pleease contact me through messages'); // Handle typo version

    // Use current note for display in edit dialog, or default note if it's an old English default
    final displayNote = isOldEnglishDefault ? defaultNote : currentNote;
    final controller = TextEditingController(text: displayNote);

    // If this is an old English default, automatically update it in the database
    if (isOldEnglishDefault) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onEditNote(defaultNote);
      });
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getCategoryColor(widget.category.type).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getCategoryIcon(widget.category.type),
                color: _getCategoryColor(widget.category.type),
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                AppLocalizations.of(context)!.editCategory(widget.category.type.getDisplayName(context)),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)?.description ?? 'Description',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: controller,
              maxLines: 4,
              decoration: InputDecoration(
                hintText: widget.category.type.getDefaultNote(context),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: _getCategoryColor(widget.category.type),
                    width: 2,
                  ),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey[600],
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
            child: Text(
              AppLocalizations.of(context)?.cancel ?? 'Cancel',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onEditNote(controller.text);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: _getCategoryColor(widget.category.type),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              elevation: 0,
            ),
            child: Text(
              AppLocalizations.of(context)?.save ?? 'Save',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }
}
