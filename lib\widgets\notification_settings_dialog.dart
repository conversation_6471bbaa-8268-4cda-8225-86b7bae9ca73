import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/notification_service.dart';

class NotificationSettingsDialog extends StatefulWidget {
  const NotificationSettingsDialog({super.key});

  @override
  State<NotificationSettingsDialog> createState() => _NotificationSettingsDialogState();
}

class _NotificationSettingsDialogState extends State<NotificationSettingsDialog> {
  bool _isLoading = true;
  bool _canScheduleExact = false;
  String _statusMessage = '';

  @override
  void initState() {
    super.initState();
    _checkNotificationStatus();
  }

  Future<void> _checkNotificationStatus() async {
    try {
      final canScheduleExact = await NotificationService.canScheduleExactAlarms();
      final statusMessage = await NotificationService.getNotificationStatusMessage();
      
      if (mounted) {
        setState(() {
          _canScheduleExact = canScheduleExact;
          _statusMessage = statusMessage;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _statusMessage = 'Error checking notification status: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _testNotification() async {
    try {
      // Show dialog to choose test type
      final testType = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Choose Test Type'),
          content: const Text('Select which notification test to run:'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop('quick'),
              child: const Text('Quick Test'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop('comprehensive'),
              child: const Text('Comprehensive Test'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        ),
      );

      if (testType == null) return;

      if (testType == 'quick') {
        await NotificationService.showTestNotification();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Quick test notification sent!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }
      } else if (testType == 'comprehensive') {
        await NotificationService.runNotificationReliabilityTests();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Comprehensive tests started! Check notifications over next 2 minutes.'),
              backgroundColor: Colors.blue,
              duration: Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send test notification: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _openAppSettings() async {
    try {
      // Try multiple approaches to open settings
      bool opened = false;

      // Method 1: Try to open app-specific settings
      try {
        await launchUrl(
          Uri.parse('android-app://com.android.settings/.Settings\$AppInfoSettingsActivity?package=com.example.contact_times'),
          mode: LaunchMode.externalApplication,
        );
        opened = true;
      } catch (e) {
        debugPrint('Method 1 failed: $e');
      }

      // Method 2: Try general app settings
      if (!opened) {
        try {
          await launchUrl(
            Uri.parse('android-app://com.android.settings/.Settings\$APPLICATION_SETTINGS'),
            mode: LaunchMode.externalApplication,
          );
          opened = true;
        } catch (e) {
          debugPrint('Method 2 failed: $e');
        }
      }

      // Method 3: Fallback to general settings
      if (!opened) {
        await launchUrl(
          Uri.parse('android-app://com.android.settings'),
          mode: LaunchMode.externalApplication,
        );
        opened = true;
      }

      if (opened && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Settings opened. Look for "Contact Times" app and enable "Alarms & reminders" permission.'),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 5),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open settings automatically. Please manually go to Settings > Apps > Contact Times > Permissions'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 6),
          ),
        );
      }
    }
  }

  Future<void> _requestExactPermission() async {
    try {
      final granted = await NotificationService.requestExactAlarmsPermission();

      if (mounted) {
        if (granted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Exact alarms permission granted! Notifications will now have precise timing.'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
          // Refresh the status
          _checkNotificationStatus();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Permission not granted. App will use alternative timing methods.'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error requesting permission: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _canScheduleExact ? Colors.green[50] : Colors.orange[50],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              _canScheduleExact ? Icons.notifications_active : Icons.notifications,
              color: _canScheduleExact ? Colors.green[600] : Colors.orange[600],
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'Notification Settings',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      content: _isLoading
          ? const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Checking notification status...'),
              ],
            )
          : Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _canScheduleExact ? Colors.green[50] : Colors.orange[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _canScheduleExact ? Colors.green[200]! : Colors.orange[200]!,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            _canScheduleExact ? Icons.check_circle : Icons.warning,
                            color: _canScheduleExact ? Colors.green[600] : Colors.orange[600],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _canScheduleExact ? 'Precise Timing Enabled' : 'Limited Timing',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: _canScheduleExact ? Colors.green[800] : Colors.orange[800],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _statusMessage,
                        style: TextStyle(
                          fontSize: 13,
                          color: _canScheduleExact ? Colors.green[700] : Colors.orange[700],
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
                if (!_canScheduleExact) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'To enable precise timing:',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[800],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Method 1 - App Permissions:\n1. Go to Settings → Apps → Contact Times\n2. Tap "Permissions"\n3. Find "Alarms & reminders"\n4. Toggle it ON\n\nMethod 2 - Special App Access:\n1. Go to Settings → Special app access\n2. Find "Alarms & reminders"\n3. Find "Contact Times" and toggle ON\n\nIf toggle doesn\'t work:\n• Force close Settings app\n• Restart your phone\n• Try again',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.blue[700],
                            height: 1.4,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.amber[50],
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(color: Colors.amber[200]!),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '⚠️ Toggle Not Working?',
                                style: TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.amber[800],
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '• Toggle frozen/unresponsive? This is common on many devices\n• App works fine without this permission using alternative timing\n• Restart phone if you want to try enabling it\n• Alternative timing is reliable for most users',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.amber[700],
                                  height: 1.3,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(color: Colors.blue[200]!),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '🔋 For Best Reliability:',
                                style: TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue[800],
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '• Disable battery optimization for this app\n• Add to "Auto-start" or "Protected apps" list\n• Keep notifications enabled in system settings\n• Allow notifications during Do Not Disturb',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.blue[700],
                                  height: 1.3,
                                ),
                              ),
                              const SizedBox(height: 6),
                              GestureDetector(
                                onTap: () async {
                                  final messenger = ScaffoldMessenger.of(context);
                                  await NotificationService.showBatteryOptimizationGuidance();
                                  if (mounted) {
                                    messenger.showSnackBar(
                                      const SnackBar(
                                        content: Text('Battery optimization guidance logged to console'),
                                        backgroundColor: Colors.blue,
                                        duration: Duration(seconds: 2),
                                      ),
                                    );
                                  }
                                },
                                child: Text(
                                  'Tap for detailed battery optimization steps →',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.blue[600],
                                    fontWeight: FontWeight.w600,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: _openAppSettings,
                                icon: const Icon(Icons.settings, size: 16),
                                label: const Text(
                                  'Open Settings',
                                  style: TextStyle(fontSize: 12),
                                ),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.blue[700],
                                  side: BorderSide(color: Colors.blue[300]!),
                                  padding: const EdgeInsets.symmetric(vertical: 8),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: _requestExactPermission,
                                icon: const Icon(Icons.alarm, size: 16),
                                label: const Text(
                                  'Request Permission',
                                  style: TextStyle(fontSize: 12),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue[600],
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 8),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
                const SizedBox(height: 16),
                Text(
                  'How it works:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '• Notifications appear 5 minutes before contact time slots\n• Only enabled for time slots you\'ve selected\n• Automatically schedules for recurring weekly times',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    height: 1.4,
                  ),
                ),
              ],
            ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Close',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
        ),
        if (!_canScheduleExact && !_isLoading)
          TextButton.icon(
            onPressed: () {
              _checkNotificationStatus();
            },
            icon: const Icon(Icons.refresh, size: 16),
            label: const Text(
              'Refresh',
              style: TextStyle(fontSize: 14),
            ),
            style: TextButton.styleFrom(
              foregroundColor: Colors.blue[600],
            ),
          ),
        ElevatedButton(
          onPressed: _testNotification,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue[600],
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          ),
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.send, size: 16),
              SizedBox(width: 8),
              Text(
                'Test',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
