# contact_times


# Project: Contact Time App

## 1. Core Concept

**Contact Time** is a Flutter-based mobile application that allows users to manage when they can be contacted by different people. Users categorize their contacts, and each category has specific rules (e.g., "Contact me anytime," "Contact me only at these times"). When another user of the app wants to call them, they will see the specific communication rules and preferred times set for them, reducing unwanted calls during busy periods.

## 2. Problem Solved

The app aims to solve the common problem of receiving phone calls at inconvenient or inappropriate times from various groups of people (family, friends, co-workers). It provides a proactive way for users to set communication boundaries without needing to manually decline calls or send "I'm busy" messages.

## 3. Core User Flow

### User A (The "Scheduler")
1.  **Downloads the app** and authenticates.
2.  **Syncs device contacts** with the app.
3.  Goes to the **"Contacts Distribution"** page.
4.  Contacts are presented one by one in a **Tinder-style card UI**.
5.  For each contact, User A swipes or taps to assign them to one of the predefined **categories**.
6.  User A can customize the **time slots** for categories that require them (e.g., "Contact me at these times").
7.  The app saves these preferences to the **Supabase** server, linking each of User A's contacts (identified by their phone number/s) to a specific category.
8.  User A can share their profile or let others know they are using the app.

### User B (The "Caller")
1.  **Downloads the app** and authenticates.
2.  In their contacts page within the app, they see a list of their own device contacts who are also using the app.
3.  User B wants to call User A. They find **User A's name** in the list (this is only visible because User B already has User A's number/s saved on their device).
4.  User B taps on User A's name.
5.  The app displays the **category note and time slots** that User A assigned to User B.
6.  User B clicks the **phone icon** to initiate a call.

## 4. Key Features

### a. User Authentication
* Simple sign-up/sign-in process (Email/Password.).
* User session management using **Supabase Auth**.
* User data stored in a `profiles` table in Supabase.

### b. Contact Categorization
* A dedicated screen for sorting unsorted contacts.
* **UI:** Tinder-like swipeable cards to quickly assign a contact to a category.
* The system stores the relationship between the current user, their contact's phone number/s, and the assigned category ID.

### c. Predefined Categories
The user can assign their contacts to one of four categories.

1.  **Contact Me Anytime**
    * **Note:** "You can contact me at any time."
    * **Time Slots:** None.

2.  **Prefer to be Contacted Anytime (with suggestions)**
    * **Note:** "You can contact me at any time, but I'd prefer if you use these times if the call can wait."
    * **Time Slots:** Yes, user-defined available time slots are displayed.

3.  **Contact Me At These Times**
    * **Note:** "Please contact me at these times."
    * **Time Slots:** Yes, user-defined required time slots are displayed. Calls outside these times are discouraged.

4.  **Contact Me Through Messages**
    * **Note:** "Please contact me through messages at the following times."
    * **Time Slots:** Yes, user-defined time slots for messaging are displayed.

### d. The "Calling" Action Flow
When User B clicks the phone icon to call User A, the following three actions occur simultaneously:

1.  **Local Notification (for User B):** A local notification is displayed on User B's device.
    * **If it's a good time to call:** The notification says something like, "Now is a good time to call [User A's Name]."
    * **If it's a bad time to call:** The notification says, "This is outside of [User A's Name]'s preferred time. Do you want to continue?" and includes a "Notify Me" button to get an alert when the user becomes available.

2.  **Push Notification (to User A):** A push notification is sent from User B's app to User A's device via **Supabase Edge Functions**.
    * **Content:** The notification on User A's device will say something like: "[User B's Name] ([Category You Assigned]) is calling. They are calling at the correct time." (or "...at an inconvenient time."). This gives User A immediate context before they even answer the phone.

3.  **Launch Phone Dialer:** The app uses the `url_launcher` package to open the default phone dialer on User B's device with User A's number pre-filled.

## 5. Screen / Page Breakdown

1.  **Splash Screen:** App logo and loading indicator.
2.  **Authentication Pages:** Sign In / Sign Up screens.
3.  **Dashboard:** Main landing page after login. Could show stats.
4.  **Categories Page:** A page where the user can view the four categories and edit the time slots associated with them.
5.  **Contacts Distribution Page:** The core Tinder-style UI for assigning device contacts to categories.
6.  **Contacts Page:** The user's main contact list. It shows which of their contacts are on the app. Tapping a user reveals the communication rules they've set.
7.  **Messages Page:** (Functionality to be defined, but likely for in-app messaging or viewing call requests).
8.  **Profile Page:** User can edit their name, profile picture, and manage their account.

## 6. Technical Stack & Data Models

* **Frontend:** Flutter
* **Backend:** Supabase
    * **Database:** PostgreSQL
    * **Authentication:** Supabase Auth
    * **Realtime/Push Notifications:** Supabase Edge Functions & Realtime.

### Proposed(examples) Supabase Database Schema:
note: please create seperate sql file.

anon public key : eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.x8DPiGXkx4ykGzPQhazEoX4m4Hiya1kvMj1_P4FSUjU

url: https://piuefmjyoamlieapemmk.supabase.co

**`profiles` table:**
* `id` (uuid, references `auth.users.id`) - Primary Key
* `username` (text)
* `full_name` (text)
* `avatar_url` (text)
* `phone_number` (text, unique) - *Crucial for matching users.*

**`categories` table:**
* `id` (uuid) - Primary Key
* `user_id` (uuid, foreign key to `profiles.id`) - The user who defines these settings.
* `type` (integer) - e.g., 1 for Anytime, 2 for Anytime with suggestions, etc.
* `note` (text) - The message shown to the caller.

**`time_slots` table:**
* `id` (uuid) - Primary Key
* `category_id` (uuid, foreign key to `categories.id`)
* `day_of_week` (integer) - 0 for Sunday, 1 for Monday, etc.
* `start_time` (time)
* `end_time` (time)

**`user_contacts` table:**
* `id` (uuid) - Primary Key
* `user_id` (uuid, foreign key to `profiles.id`) - The user who is doing the categorizing.
* `categorized_contact_phone` (text) - The phone number/s of the contact being categorized.
* `assigned_category_id` (uuid, foreign key to `categories.id`) - The category this contact is placed in.