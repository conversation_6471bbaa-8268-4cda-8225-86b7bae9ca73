import 'package:flutter_test/flutter_test.dart';
import 'package:contact_times/models/time_slot.dart';

void main() {
  group('TimeSlot Merging Tests', () {
    test('should merge overlapping time slots on same day', () {
      final slot1 = TimeSlot(
        id: '1',
        categoryId: 'cat1',
        dayOfWeek: 1, // Monday
        startTime: const CustomTimeOfDay(hour: 9, minute: 0),
        endTime: const CustomTimeOfDay(hour: 12, minute: 0),
        createdAt: DateTime.now(),
      );

      final slot2 = TimeSlot(
        id: '2',
        categoryId: 'cat1',
        dayOfWeek: 1, // Monday
        startTime: const CustomTimeOfDay(hour: 11, minute: 0),
        endTime: const CustomTimeOfDay(hour: 15, minute: 0),
        createdAt: DateTime.now(),
      );

      final mergedSlots = TimeSlot.mergeOverlappingSlots([slot1, slot2]);

      expect(mergedSlots.length, 1);
      expect(mergedSlots[0].startTime.hour, 9);
      expect(mergedSlots[0].startTime.minute, 0);
      expect(mergedSlots[0].endTime.hour, 15);
      expect(mergedSlots[0].endTime.minute, 0);
    });

    test('should merge adjacent time slots on same day', () {
      final slot1 = TimeSlot(
        id: '1',
        categoryId: 'cat1',
        dayOfWeek: 1, // Monday
        startTime: const CustomTimeOfDay(hour: 9, minute: 0),
        endTime: const CustomTimeOfDay(hour: 12, minute: 0),
        createdAt: DateTime.now(),
      );

      final slot2 = TimeSlot(
        id: '2',
        categoryId: 'cat1',
        dayOfWeek: 1, // Monday
        startTime: const CustomTimeOfDay(hour: 12, minute: 0),
        endTime: const CustomTimeOfDay(hour: 15, minute: 0),
        createdAt: DateTime.now(),
      );

      final mergedSlots = TimeSlot.mergeOverlappingSlots([slot1, slot2]);

      expect(mergedSlots.length, 1);
      expect(mergedSlots[0].startTime.hour, 9);
      expect(mergedSlots[0].startTime.minute, 0);
      expect(mergedSlots[0].endTime.hour, 15);
      expect(mergedSlots[0].endTime.minute, 0);
    });

    test('should not merge time slots on different days', () {
      final slot1 = TimeSlot(
        id: '1',
        categoryId: 'cat1',
        dayOfWeek: 1, // Monday
        startTime: const CustomTimeOfDay(hour: 9, minute: 0),
        endTime: const CustomTimeOfDay(hour: 12, minute: 0),
        createdAt: DateTime.now(),
      );

      final slot2 = TimeSlot(
        id: '2',
        categoryId: 'cat1',
        dayOfWeek: 2, // Tuesday
        startTime: const CustomTimeOfDay(hour: 9, minute: 0),
        endTime: const CustomTimeOfDay(hour: 12, minute: 0),
        createdAt: DateTime.now(),
      );

      final mergedSlots = TimeSlot.mergeOverlappingSlots([slot1, slot2]);

      expect(mergedSlots.length, 2);
    });

    test('should not merge non-overlapping time slots on same day', () {
      final slot1 = TimeSlot(
        id: '1',
        categoryId: 'cat1',
        dayOfWeek: 1, // Monday
        startTime: const CustomTimeOfDay(hour: 9, minute: 0),
        endTime: const CustomTimeOfDay(hour: 11, minute: 0),
        createdAt: DateTime.now(),
      );

      final slot2 = TimeSlot(
        id: '2',
        categoryId: 'cat1',
        dayOfWeek: 1, // Monday
        startTime: const CustomTimeOfDay(hour: 13, minute: 0),
        endTime: const CustomTimeOfDay(hour: 15, minute: 0),
        createdAt: DateTime.now(),
      );

      final mergedSlots = TimeSlot.mergeOverlappingSlots([slot1, slot2]);

      expect(mergedSlots.length, 2);
    });

    test('should merge multiple overlapping time slots', () {
      final slot1 = TimeSlot(
        id: '1',
        categoryId: 'cat1',
        dayOfWeek: 1, // Monday
        startTime: const CustomTimeOfDay(hour: 9, minute: 0),
        endTime: const CustomTimeOfDay(hour: 11, minute: 0),
        createdAt: DateTime.now(),
      );

      final slot2 = TimeSlot(
        id: '2',
        categoryId: 'cat1',
        dayOfWeek: 1, // Monday
        startTime: const CustomTimeOfDay(hour: 10, minute: 30),
        endTime: const CustomTimeOfDay(hour: 13, minute: 0),
        createdAt: DateTime.now(),
      );

      final slot3 = TimeSlot(
        id: '3',
        categoryId: 'cat1',
        dayOfWeek: 1, // Monday
        startTime: const CustomTimeOfDay(hour: 12, minute: 30),
        endTime: const CustomTimeOfDay(hour: 15, minute: 0),
        createdAt: DateTime.now(),
      );

      final mergedSlots = TimeSlot.mergeOverlappingSlots([slot1, slot2, slot3]);

      expect(mergedSlots.length, 1);
      expect(mergedSlots[0].startTime.hour, 9);
      expect(mergedSlots[0].startTime.minute, 0);
      expect(mergedSlots[0].endTime.hour, 15);
      expect(mergedSlots[0].endTime.minute, 0);
    });

    test('should handle complex merging scenario', () {
      final slots = [
        TimeSlot(
          id: '1',
          categoryId: 'cat1',
          dayOfWeek: 1, // Monday
          startTime: const CustomTimeOfDay(hour: 9, minute: 0),
          endTime: const CustomTimeOfDay(hour: 11, minute: 0),
          createdAt: DateTime.now(),
        ),
        TimeSlot(
          id: '2',
          categoryId: 'cat1',
          dayOfWeek: 1, // Monday
          startTime: const CustomTimeOfDay(hour: 10, minute: 0),
          endTime: const CustomTimeOfDay(hour: 12, minute: 0),
          createdAt: DateTime.now(),
        ),
        TimeSlot(
          id: '3',
          categoryId: 'cat1',
          dayOfWeek: 1, // Monday
          startTime: const CustomTimeOfDay(hour: 14, minute: 0),
          endTime: const CustomTimeOfDay(hour: 16, minute: 0),
          createdAt: DateTime.now(),
        ),
        TimeSlot(
          id: '4',
          categoryId: 'cat1',
          dayOfWeek: 2, // Tuesday
          startTime: const CustomTimeOfDay(hour: 9, minute: 0),
          endTime: const CustomTimeOfDay(hour: 11, minute: 0),
          createdAt: DateTime.now(),
        ),
      ];

      final mergedSlots = TimeSlot.mergeOverlappingSlots(slots);

      expect(mergedSlots.length, 3); // 2 on Monday (merged + separate) + 1 on Tuesday
      
      // Find Monday slots
      final mondaySlots = mergedSlots.where((slot) => slot.dayOfWeek == 1).toList();
      expect(mondaySlots.length, 2);
      
      // Find Tuesday slots
      final tuesdaySlots = mergedSlots.where((slot) => slot.dayOfWeek == 2).toList();
      expect(tuesdaySlots.length, 1);
    });
  });
}
