import 'package:flutter/material.dart';
import 'package:flutter/material.dart' as material;
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../models/models.dart';
import '../../models/category_extensions.dart';
import '../../services/connectivity_service.dart';
import '../../services/local_database_service.dart';
import '../../services/offline_contact_service.dart';
import '../../widgets/ltr_override.dart';

class TimeSlotsEditor extends StatefulWidget {
  final Category category;
  final Function(List<TimeSlot>) onSaved;

  const TimeSlotsEditor({
    super.key,
    required this.category,
    required this.onSaved,
  });

  @override
  State<TimeSlotsEditor> createState() => _TimeSlotsEditorState();
}

class _TimeSlotsEditorState extends State<TimeSlotsEditor> {
  List<TimeSlotData> _timeSlots = [];
  bool _isLoading = false;
  final ConnectivityService _connectivityService = ConnectivityService();
  final OfflineContactService _offlineService = OfflineContactService();

  @override
  void initState() {
    super.initState();
    _initializeTimeSlots();
  }

  void _initializeTimeSlots() {
    _timeSlots = widget.category.timeSlots
        .map((slot) => TimeSlotData(
              id: slot.id,
              dayOfWeek: slot.dayOfWeek,
              startTime: material.TimeOfDay(hour: slot.startTime.hour, minute: slot.startTime.minute),
              endTime: material.TimeOfDay(hour: slot.endTime.hour, minute: slot.endTime.minute),
            ))
        .toList();
  }

  void _addTimeSlot() {
    setState(() {
      _timeSlots.add(TimeSlotData(
        dayOfWeek: 1, // Monday
        startTime: const material.TimeOfDay(hour: 9, minute: 0),
        endTime: const material.TimeOfDay(hour: 17, minute: 0),
      ));
    });
  }

  void _removeTimeSlot(int index) {
    setState(() {
      _timeSlots.removeAt(index);
    });
  }

  /// Check if there are any overlapping time slots
  bool _hasOverlappingSlots() {
    final timeSlots = _timeSlots
        .map((data) => TimeSlot(
              id: data.id ?? '',
              categoryId: widget.category.id,
              dayOfWeek: data.dayOfWeek,
              startTime: CustomTimeOfDay(hour: data.startTime.hour, minute: data.startTime.minute),
              endTime: CustomTimeOfDay(hour: data.endTime.hour, minute: data.endTime.minute),
              createdAt: DateTime.now(),
            ))
        .toList();

    final mergedSlots = TimeSlot.mergeOverlappingSlots(timeSlots);
    return mergedSlots.length < timeSlots.length;
  }

  /// Get overlapping slot groups for preview
  List<List<int>> _getOverlappingGroups() {
    final List<List<int>> overlappingGroups = [];
    final Set<int> processedIndices = {};

    for (int i = 0; i < _timeSlots.length; i++) {
      if (processedIndices.contains(i)) continue;

      final List<int> currentGroup = [i];
      processedIndices.add(i);

      final currentSlot = _timeSlots[i];
      final currentTimeSlot = TimeSlot(
        id: '',
        categoryId: widget.category.id,
        dayOfWeek: currentSlot.dayOfWeek,
        startTime: CustomTimeOfDay(hour: currentSlot.startTime.hour, minute: currentSlot.startTime.minute),
        endTime: CustomTimeOfDay(hour: currentSlot.endTime.hour, minute: currentSlot.endTime.minute),
        createdAt: DateTime.now(),
      );

      for (int j = i + 1; j < _timeSlots.length; j++) {
        if (processedIndices.contains(j)) continue;

        final otherSlot = _timeSlots[j];
        final otherTimeSlot = TimeSlot(
          id: '',
          categoryId: widget.category.id,
          dayOfWeek: otherSlot.dayOfWeek,
          startTime: CustomTimeOfDay(hour: otherSlot.startTime.hour, minute: otherSlot.startTime.minute),
          endTime: CustomTimeOfDay(hour: otherSlot.endTime.hour, minute: otherSlot.endTime.minute),
          createdAt: DateTime.now(),
        );

        if (currentTimeSlot.overlapsWith(otherTimeSlot) || currentTimeSlot.isAdjacentTo(otherTimeSlot)) {
          currentGroup.add(j);
          processedIndices.add(j);
        }
      }

      if (currentGroup.length > 1) {
        overlappingGroups.add(currentGroup);
      }
    }

    return overlappingGroups;
  }

  Future<void> _saveTimeSlots() async {
    try {
      print('📱 ==================== TIME SLOTS SAVE START ====================');
      print('📱 Time slots count: ${_timeSlots.length}');
      print('📱 Category ID: ${widget.category.id}');
      print('📱 Category type: ${widget.category.type.name}');
      print('📱 Connectivity: ${_connectivityService.isOnline ? 'ONLINE' : 'OFFLINE'}');

      if (_timeSlots.isEmpty) {
        print('📱 ⚠️ No time slots to save');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)?.pleaseAddAtLeastOneTimeSlot ?? 'Please add at least one time slot'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      setState(() {
        _isLoading = true;
      });

      // Validate all time slots before processing
      print('📱 Validating time slots...');
      for (int i = 0; i < _timeSlots.length; i++) {
        final data = _timeSlots[i];
        print('📱   [$i] Validating: Day=${data.dayOfWeek}, Start=${data.startTime}, End=${data.endTime}');

        if (data.dayOfWeek < 0 || data.dayOfWeek > 6) {
          throw Exception('Invalid day of week: ${data.dayOfWeek} (must be 0-6)');
        }

        if (data.startTime.hour >= data.endTime.hour &&
            (data.startTime.hour > data.endTime.hour || data.startTime.minute >= data.endTime.minute)) {
          final startTimeStr = '${data.startTime.hour.toString().padLeft(2, '0')}:${data.startTime.minute.toString().padLeft(2, '0')}';
          final endTimeStr = '${data.endTime.hour.toString().padLeft(2, '0')}:${data.endTime.minute.toString().padLeft(2, '0')}';
          throw Exception(AppLocalizations.of(context)?.invalidTimeSlotRange(startTimeStr, endTimeStr) ?? 'Start time must be before end time: $startTimeStr >= $endTimeStr');
        }

        print('📱   [$i] ✅ Valid time slot');
      }

      // Convert TimeSlotData to TimeSlot objects
      print('📱 Converting ${_timeSlots.length} TimeSlotData to TimeSlot objects...');
      final timeSlots = <TimeSlot>[];

      for (int i = 0; i < _timeSlots.length; i++) {
        final data = _timeSlots[i];

        // For new time slots (no ID), let Supabase generate the UUID
        // For existing time slots, keep the existing ID
        final id = data.id ?? '';

        print('📱   [$i] Creating TimeSlot:');
        print('📱       ID: ${id.isEmpty ? 'AUTO_GENERATE' : id}');
        print('📱       Day: ${data.dayOfWeek} (${data.dayName})');
        print('📱       Time: ${data.startTime} - ${data.endTime}');

        final timeSlot = TimeSlot(
          id: id,
          categoryId: widget.category.id,
          dayOfWeek: data.dayOfWeek,
          startTime: CustomTimeOfDay(hour: data.startTime.hour, minute: data.startTime.minute),
          endTime: CustomTimeOfDay(hour: data.endTime.hour, minute: data.endTime.minute),
          createdAt: DateTime.now(),
        );

        timeSlots.add(timeSlot);
        print('📱   [$i] ✅ TimeSlot created successfully');
      }

      // Merge overlapping time slots before saving
      final mergedTimeSlots = TimeSlot.mergeOverlappingSlots(timeSlots);

      // Check if any merging occurred and show notification
      if (mergedTimeSlots.length < timeSlots.length) {
        final mergedCount = timeSlots.length - mergedTimeSlots.length;
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)?.mergedOverlappingTimeSlots(mergedCount, mergedCount > 1 ? 's' : '') ?? 'Merged $mergedCount overlapping time slot${mergedCount > 1 ? 's' : ''}'),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }

      // Create updated category with new time slots
      print('📱 Creating updated category with ${mergedTimeSlots.length} merged time slots...');
      final updatedCategory = widget.category.copyWith(timeSlots: mergedTimeSlots);
      print('📱 Updated category: ${updatedCategory.type.name} (ID: ${updatedCategory.id})');
      for (final slot in updatedCategory.timeSlots) {
        print('📱   Time slot: ${slot.dayName} ${slot.timeRange} (ID: ${slot.id})');
      }

      // Always save to local database first for immediate persistence
      print('📱 Saving updated category to local database...');
      await LocalDatabaseService.insertCategory(updatedCategory);
      print('📱 ✅ Category saved to local database');

      // Refresh the offline service cache to reflect the changes
      await _offlineService.refreshCategoriesCache();

      // Call the onSaved callback to update the parent screen
      widget.onSaved(mergedTimeSlots);

      // Always close the editor and let the parent screen handle sync
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)?.failedToSaveTimeSlots(e.toString()) ?? 'Failed to save time slots: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context)?.editTimeSlots ?? 'Edit Time Slots',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 24,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF667eea),
                Color(0xFF764ba2),
              ],
            ),
          ),
        ),
        actions: [
          // Connectivity indicator
          if (!_connectivityService.isOnline) ...[
            Container(
              margin: const EdgeInsets.only(right: 8),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.9),
                borderRadius: BorderRadius.circular(12),
              ),
              child:  Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.wifi_off, color: Colors.white, size: 16),
                  SizedBox(width: 4),
                  Text(
                    AppLocalizations.of(context)?.offline ?? 'Offline',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
          // Category type indicator
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  widget.category.type.icon,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  widget.category.type.getDisplayName(context),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: TimeSlotEditorLTR(
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF667eea),
                Color(0xFF764ba2),
              ],
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Header section with category info
                _buildHeaderSection(),

                // Time slots list
                Expanded(
                  child: _timeSlots.isEmpty
                      ? _buildEmptyState()
                      : _buildTimeSlotsList(),
                ),

                // Bottom action buttons
                _buildBottomActions(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.95),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  widget.category.type.icon,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.category.type.getDisplayName(context),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D3748),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      AppLocalizations.of(context)?.timeSlotsCountSimple(_timeSlots.length, _timeSlots.length != 1 ? 's' : '') ?? '${_timeSlots.length} time slot${_timeSlots.length != 1 ? 's' : ''}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (widget.category.note.isNotEmpty) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF667eea).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(0xFF667eea).withOpacity(0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: const Color(0xFF667eea),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.category.type.getDefaultNote(context),
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[700],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(32),
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.95),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              spreadRadius: 0,
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                ),
                borderRadius: BorderRadius.circular(50),
              ),
              child: const Icon(
                Icons.schedule,
                size: 48,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              AppLocalizations.of(context)?.noTimeSlotsYet ?? 'No Time Slots Yet',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2D3748),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              AppLocalizations.of(context)?.addYourFirstTimeSlot ?? 'Add your first time slot to get started.\nLet others know when you\'re available!',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                height: 1.5,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _addTimeSlot,
              icon: const Icon(Icons.add, size: 20),
              label: Text(
                AppLocalizations.of(context)?.addYourFirstTimeSlotButton ?? 'Add Your First Time Slot',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667eea),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSlotsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _timeSlots.length,
      itemBuilder: (context, index) {
        final overlappingGroups = _getOverlappingGroups();
        final hasOverlap = overlappingGroups.any((group) => group.contains(index));

        return ModernTimeSlotCard(
          timeSlot: _timeSlots[index],
          index: index,
          hasOverlap: hasOverlap,
          onChanged: (updatedSlot) {
            setState(() {
              _timeSlots[index] = updatedSlot;
            });
          },
          onRemove: () => _removeTimeSlot(index),
        );
      },
    );
  }

  Widget _buildBottomActions() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.95),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _addTimeSlot,
              icon: const Icon(Icons.add, size: 20),
              label: Text(
                AppLocalizations.of(context)?.addTimeSlot ?? 'Add Time Slot',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667eea),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _saveTimeSlots,
              icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.save, size: 20),
              label: Text(
                _isLoading ? (AppLocalizations.of(context)?.saving ?? 'Saving...') : (AppLocalizations.of(context)?.saveChanges ?? 'Save Changes'),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: _isLoading ? Colors.grey : Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
            ),
          ),
        ],
      ),
    );
  }


}

class TimeSlotData {
  final String? id;
  final int dayOfWeek;
  final material.TimeOfDay startTime;
  final material.TimeOfDay endTime;

  TimeSlotData({
    this.id,
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
  });

  TimeSlotData copyWith({
    String? id,
    int? dayOfWeek,
    material.TimeOfDay? startTime,
    material.TimeOfDay? endTime,
  }) {
    return TimeSlotData(
      id: id ?? this.id,
      dayOfWeek: dayOfWeek ?? this.dayOfWeek,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
    );
  }

  String get dayName {
    const days = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday'
    ];
    return days[dayOfWeek];
  }

  String getDayName(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    if (l10n == null) return dayName;

    switch (dayOfWeek) {
      case 0: return l10n.sunday;
      case 1: return l10n.monday;
      case 2: return l10n.tuesday;
      case 3: return l10n.wednesday;
      case 4: return l10n.thursday;
      case 5: return l10n.friday;
      case 6: return l10n.saturday;
      default: return dayName;
    }
  }
}

class ModernTimeSlotCard extends StatefulWidget {
  final TimeSlotData timeSlot;
  final int index;
  final Function(TimeSlotData) onChanged;
  final VoidCallback onRemove;
  final bool hasOverlap;

  const ModernTimeSlotCard({
    super.key,
    required this.timeSlot,
    required this.index,
    required this.onChanged,
    required this.onRemove,
    this.hasOverlap = false,
  });

  @override
  State<ModernTimeSlotCard> createState() => _ModernTimeSlotCardState();
}

class _ModernTimeSlotCardState extends State<ModernTimeSlotCard>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  List<String> _getLocalizedDays(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    if (l10n == null) {
      return ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']; // Arabic fallback since Arabic is default
    }
    return [l10n.sunday, l10n.monday, l10n.tuesday, l10n.wednesday, l10n.thursday, l10n.friday, l10n.saturday];
  }

  @override
  Widget build(BuildContext context) {
    final days = _getLocalizedDays(context);

    return TimeDisplayLTR(
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _scaleController.forward(),
            onTapUp: (_) => _scaleController.reverse(),
            onTapCancel: () => _scaleController.reverse(),
            child: Container(
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.95),
                borderRadius: BorderRadius.circular(16),
                border: widget.hasOverlap
                    ? Border.all(color: Colors.orange, width: 2)
                    : null,
                boxShadow: [
                  BoxShadow(
                    color: widget.hasOverlap
                        ? Colors.orange.withOpacity(0.3)
                        : Colors.black.withOpacity(0.1),
                    spreadRadius: 0,
                    blurRadius: widget.hasOverlap ? 15 : 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with day and delete button
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: widget.hasOverlap
                                  ? [Colors.orange, Colors.deepOrange]
                                  : [const Color(0xFF667eea), const Color(0xFF764ba2)],
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.schedule,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppLocalizations.of(context)?.timeSlotNumber(widget.index + 1) ?? 'Time Slot ${widget.index + 1}',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF2D3748),
                                ),
                              ),
                              Text(
                                days[widget.timeSlot.dayOfWeek],
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          onPressed: widget.onRemove,
                          icon: const Icon(Icons.delete_outline),
                          color: Colors.red[400],
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.red[50],
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Overlap warning
                    if (widget.hasOverlap) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange[200]!),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.warning_amber_rounded,
                                 color: Colors.orange[700], size: 20),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                AppLocalizations.of(context)?.timeSlotOverlapWarning ?? 'This slot overlaps with others and will be merged when saved',
                                style: TextStyle(
                                  color: Colors.orange[800],
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],

                    const SizedBox(height: 20),

                    // Day selector
                    Text(
                      AppLocalizations.of(context)?.dayOfWeek ?? 'Day of Week',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[200]!),
                      ),
                      child: DropdownButtonFormField<int>(
                        value: widget.timeSlot.dayOfWeek,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        ),
                        items: List.generate(7, (index) {
                          return DropdownMenuItem(
                            value: index,
                            child: Text(
                              days[index],
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          );
                        }),
                        onChanged: (value) {
                          if (value != null) {
                            widget.onChanged(widget.timeSlot.copyWith(dayOfWeek: value));
                          }
                        },
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Time pickers
                    Row(
                      children: [
                        Expanded(
                          child: _ModernTimePickerField(
                            label: AppLocalizations.of(context)?.startTime ?? 'Start Time',
                            time: widget.timeSlot.startTime,
                            onChanged: (newTime) {
                              widget.onChanged(widget.timeSlot.copyWith(startTime: newTime));
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _ModernTimePickerField(
                            label: AppLocalizations.of(context)?.endTime ?? 'End Time',
                            time: widget.timeSlot.endTime,
                            onChanged: (newTime) {
                              widget.onChanged(widget.timeSlot.copyWith(endTime: newTime));
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      ),
    );
  }
}

class _ModernTimePickerField extends StatelessWidget {
  final String label;
  final material.TimeOfDay time;
  final Function(material.TimeOfDay) onChanged;

  const _ModernTimePickerField({
    required this.label,
    required this.time,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return TimeDisplayLTR(
      child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () async {
            final newTime = await showTimePicker(
              context: context,
              initialTime: time,
              builder: (context, child) {
                return Theme(
                  data: Theme.of(context).copyWith(
                    timePickerTheme: TimePickerThemeData(
                      backgroundColor: Colors.white,
                      hourMinuteShape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      dayPeriodShape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                  child: child!,
                );
              },
            );
            if (newTime != null) {
              onChanged(newTime);
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.access_time,
                  color: Color(0xFF667eea),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    time.format(context),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF2D3748),
                    ),
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.grey[400],
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
      ),
    );
  }
}
