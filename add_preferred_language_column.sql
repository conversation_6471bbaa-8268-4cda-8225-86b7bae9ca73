-- Add preferred_language column to profiles table
-- Run this in your Supabase SQL Editor to fix the missing column error

-- Check if the column already exists and add it if it doesn't
DO $$
BEGIN
    -- Check if the preferred_language column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'profiles'
        AND column_name = 'preferred_language'
    ) THEN
        -- Add the preferred_language column
        ALTER TABLE public.profiles
        ADD COLUMN preferred_language TEXT DEFAULT 'en' CHECK (preferred_language IN ('en', 'ar'));
        
        RAISE NOTICE 'Added preferred_language column to profiles table';
    ELSE
        RAISE NOTICE 'preferred_language column already exists in profiles table';
    END IF;
END $$;

-- Update any existing profiles to have the default language
UPDATE public.profiles 
SET preferred_language = 'en' 
WHERE preferred_language IS NULL;

-- Verify the column was added
SELECT column_name, data_type, column_default, is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'profiles'
AND column_name = 'preferred_language';
